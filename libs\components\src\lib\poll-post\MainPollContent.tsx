'use client';

import { useState } from 'react';
import { Box, Typography, Divider, IconButton } from '@mui/material';
import PollOptionCard from '../poll-post/PollOptionCard';
import CustomAnswerList from '../poll-post/CustomAnswerList';
import CustomAnswerSection from '../poll-post/CustomAnswerSection';
import { Iconify } from '../iconify';
import { useTranslations } from 'next-intl';

import {
  MOCK_OPTIONS,
  CUSTOM_ANSWERS,
  POLL_TITLE,
  WINNING_OPTION,
} from '../poll-post/constants';

export default function MainPollContent() {
  const t = useTranslations('pollPost');

  const [selectedOption, setSelectedOption] = useState<string | null>(null);
  const [showCustom, setShowCustom] = useState(false);
  const [customAnswer, setCustomAnswer] = useState('');

  const totalVotes = [...MOCK_OPTIONS, ...CUSTOM_ANSWERS].reduce(
    (acc, curr) => acc + curr.votes,
    0
  );

  const handleRetractVote = () => setSelectedOption(null);

  return (
    <Box
      sx={{
        border: '1px solid #A3A3A3',
        borderRadius: '8px',
        p: '16px',
      }}
    >
      {/* Poll Title */}
      <Typography
        sx={{
          fontWeight: 600,
          fontSize: '16px',
          lineHeight: '18px',
          my: '16px',
        }}
      >
        {POLL_TITLE} {/* Or replace with t('pollTitle') if static */}
      </Typography>

      {/* Poll Options */}
      <Box display="flex" flexDirection="column" gap="16px">
        {MOCK_OPTIONS.map((option) => (
          <PollOptionCard
            key={option.label}
            label={option.label}
            votes={option.votes}
            totalVotes={totalVotes}
            selected={selectedOption === option.label}
            onSelect={() => setSelectedOption(option.label)}
          />
        ))}
      </Box>

      {/* Custom Answer Toggle */}
      <Box
        mt="16px"
        textAlign="center"
        sx={{ color: '#A24295', cursor: 'pointer' }}
        onClick={() => setShowCustom(!showCustom)}
      >
        <Typography sx={{ fontWeight: 600, fontSize: '16px' }}>
          {t('selectCustomAnswers')}
        </Typography>
        <IconButton size="small">
          <Iconify
            icon={showCustom ? 'mdi:chevron-up' : 'mdi:chevron-down'}
            color="#A24295"
          />
        </IconButton>
      </Box>

      {/* Custom Answers */}
      {showCustom && (
        <CustomAnswerList
          answers={CUSTOM_ANSWERS}
          totalVotes={totalVotes}
          selectedOption={selectedOption}
          onSelect={(label) => setSelectedOption(label)}
          onRetract={handleRetractVote}
          winningVote={WINNING_OPTION}
        />
      )}

      {/* Divider */}
      <Divider sx={{ mb: '16px', borderColor: '#A3A3A3', opacity: 0.5 }} />

      {/* Custom Answer Section */}
      <CustomAnswerSection
        value={customAnswer}
        onChange={(val) => setCustomAnswer(val)}
        onSubmit={() => console.log('Submit', customAnswer)}
      />
    </Box>
  );
}
