import { create } from 'zustand';

interface FeedFilterState {
  selectedTag: string | null;
  isFollowingTag: boolean;

  setSelectedTag: (tag: string | null) => void;
  setIsFollowingTag: (isFollowing: boolean) => void;
  resetFilters: () => void;
}

export const useFilterStore = create<FeedFilterState>((set) => ({
  selectedTag: null,
  isFollowingTag: false,

  setSelectedTag: (tag) => set({ selectedTag: tag }),
  setIsFollowingTag: (isFollowing) => set({ isFollowingTag: isFollowing }),
  resetFilters: () => set({ selectedTag: null, isFollowingTag: false }),
}));
