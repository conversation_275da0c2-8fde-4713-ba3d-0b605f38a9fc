'use client';

import { Box, IconButton } from '@mui/material';
import Image from 'next/image';
import { useSearchParams } from 'next/navigation';
import { BOOKMARK, FILTER_ICON, SEARCH_ICON, TAGS_ICON } from '../auth';
import { SearchBar } from '../search-bar/SearchBar';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import ClickAwayListener from '@mui/material/ClickAwayListener';

type MobileSearchbarProps = {
  variant?: 'default' | 'tags' | 'filter' | 'saved-posts';
};

export const MobileSearchbar = ({
  variant = 'default',
}: MobileSearchbarProps) => {
  const t = useTranslations('feedNavigation');
  const searchParams = useSearchParams();
  const folderName = decodeURIComponent(searchParams?.get('folder') || '');
  const [searchOpen, setSearchOpen] = useState(false);


  // TAGS & SAVED POSTS
  if (variant === 'tags' || variant === 'saved-posts') {
    return (
      <Box
        sx={{
          width: '100%',
          backgroundColor: 'transparent',
          marginTop: '16px',
          marginBottom: '16px',
          display: 'flex',
          justifyContent: 'center',
          px: '16px',
        }}
      >
        <SearchBar />
      </Box>
    );
  }

  // FILTER VARIANT
  if (variant === 'filter') {
    if (folderName) {
      return (
        <Box
          sx={{
            width: '100%',
            backgroundColor: 'transparent',
            marginTop: '16px',
            marginBottom: '16px',
            px: '16px',
          }}
        >
          <SearchBar />
        </Box>
      );
    }

    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          px: '16px',
          width: '100%',
          backgroundColor: 'transparent',
          marginTop: '16px',
          marginBottom: '16px',
        }}
      >
        <Box
          sx={{
            width: '64px',
            height: '45px',
            backgroundColor: 'white',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: '8px',
          }}
        >
          <Image src={SEARCH_ICON} alt={t('search')} width={32} height={32} />
        </Box>

        <IconButton
          sx={{
            width: 40,
            height: 40,
            backgroundColor: 'white',
            borderRadius: '8px',
            padding: 0,
          }}
        >
          <Image src={FILTER_ICON} alt={t('filter')} width={24} height={24} />
        </IconButton>
      </Box>
    );
  }

  // DEFAULT
  if (searchOpen) {
    return (
      <ClickAwayListener onClickAway={() => setSearchOpen(false)}>
        <Box
          sx={{
            width: '100%',
            backgroundColor: 'transparent',
            marginTop: '16px',
            marginBottom: '16px',
            display: 'flex',
            justifyContent: 'center',
            px: '16px',
            position: 'relative',
          }}
        >
          <Box sx={{ width: '100%', position: 'relative' }}>
            <SearchBar />
          </Box>
        </Box>
      </ClickAwayListener>
    );
  }

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        px: '16px',
        width: '100%',
        backgroundColor: '#F3F4F6',
        marginTop: '16px',
        marginBottom: '16px',
      }}
    >
      <Box
        sx={{
          width: '64px',
          height: '45px',
          backgroundColor: 'white',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: '8px',
          cursor: 'pointer',
        }}
        onClick={() => setSearchOpen(true)}
      >
        <Image src={SEARCH_ICON} alt={t('search')} width={32} height={32} />
      </Box>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: '20px',
        }}
      >
        <IconButton
          sx={{
            width: 40,
            height: 40,
            backgroundColor: 'white',
            borderRadius: '8px',
            padding: 0,
          }}
        >
          <Image src={FILTER_ICON} alt={t('filter')} width={24} height={24} />
        </IconButton>
        <IconButton
          sx={{
            width: 40,
            height: 40,
            backgroundColor: 'white',
            borderRadius: '8px',
            padding: 0,
          }}
        >
          <Image src={BOOKMARK} alt={t('savedPosts')} width={24} height={24} />
        </IconButton>
        <IconButton
          sx={{
            width: 40,
            height: 40,
            backgroundColor: 'white',
            borderRadius: '8px',
            padding: 0,
          }}
        >
          <Image src={TAGS_ICON} alt={t('tags')} width={24} height={24} />
        </IconButton>
      </Box>
    </Box>
  );
};
