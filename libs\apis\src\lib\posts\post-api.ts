import { AnswerComment } from '@minicardiac-client/types';
import { apiClient } from '../networking/api-client.js';
import { GetFeedParams, FeedResponse } from './types.js';

export interface Comment {
  id: string;
  content: string;
  userId: string;
  userFullName: string;
  userProfilePic?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCommentRequest {
  comment: string;
}

export interface CreateTextPostRequest {
  community: 'PROFESSIONAL' | 'PUBLIC' | 'BOTH';
  audience: 'CARDIAC_SURGEON' | 'CARDIOLOGIST' | 'BOTH';
  tags: string[];
  postStatus: 'draft' | 'published' | 'scheduled';
  postScheduleDate?: string; // ISO date string
  content: string;
}

export interface CreateQuestionPostRequest {
  community: 'PROFESSIONAL' | 'PUBLIC' | 'BOTH';
  audience: 'CARDIAC_SURGEON' | 'CARDIOLOGIST' | 'BOTH';
  tags: string[];
  postStatus: 'draft' | 'published' | 'scheduled';
  postScheduleDate?: string;
  content: string;
}

export const postApi = {
  createTextPost: async (data: CreateTextPostRequest) => {
    const response = await apiClient.post('/posts/text', data);
    return response.data;
  },

  createQuestionPost: async (data: CreateQuestionPostRequest) => {
    const response = await apiClient.post('/posts/question', data);
    return response.data;
  },

  getFeed: async (params: GetFeedParams) => {
    const response = await apiClient.get<FeedResponse>('/posts/feed', {
      params,
    });

    return response.data;
  },

  getPostById: async (postId: string) => {
    const response = await apiClient.get(`/posts/feed/${postId}`);
    return response.data.data;
  },

  likePost: async (postId: string) => {
    const response = await apiClient.post(`/posts/likes/${postId}`);
    return response.data;
  },

  unlikePost: async (postId: string) => {
    const response = await apiClient.delete(`/posts/likes/${postId}`);
    return response.data;
  },

  createComment: async (postId: string, data: CreateCommentRequest) => {
    const response = await apiClient.post(`/posts/comments/${postId}`, data);
    return response.data;
  },

  deleteComment: async (commentId: string) => {
    const response = await apiClient.delete(`/posts/comments/${commentId}`);
    return response.data;
  },

  createArticlePost: async (
    data: import('./types.js').CreateArticlePostRequest
  ) => {
    const response = await apiClient.post('/posts/article', data);
    return response.data;
  },

  //Fetch comments for a post
  getComments: async (
    postId: string,
    query?: { parentCommentId?: string; limit?: number; offset?: number }
  ): Promise<AnswerComment[]> => {
    const params = new URLSearchParams();

    if (query?.parentCommentId)
      params.append('parentCommentId', query.parentCommentId);
    if (query?.limit !== undefined)
      params.append('limit', query.limit.toString());
    if (query?.offset !== undefined)
      params.append('offset', query.offset.toString());

    const url = `/posts/comments/${postId}${
      params.toString() ? '?' + params.toString() : ''
    }`;

    const response = await apiClient.get<{ data: AnswerComment[] }>(url);
    return response.data.data;
  },

  // Pin a comment
  pinComment: async (commentId: string) => {
    const response = await apiClient.patch(
      `/posts/question/pin-comment/${commentId}`
    );
    return response;
  },

  // Unpin a comment
  unpinComment: async (postId: string) => {
    const response = await apiClient.delete(
      `/posts/question/pin-comment/${postId}`
    );
    return response;
  },
};
