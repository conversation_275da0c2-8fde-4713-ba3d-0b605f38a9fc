import { Box, Avatar, TextField, IconButton, SxProps } from '@mui/material';
import SendIcon from '@mui/icons-material/Send';
import { useCallback, useState } from 'react';
import { useComments } from '@minicardiac-client/apis';
import { toast } from 'react-toastify';

type AddCommentInputProps = {
  postId: string;
  parentCommentId?: string;
  sx?: SxProps;
};

const AddCommentInput = ({
  postId,
  parentCommentId,
  sx,
}: AddCommentInputProps) => {
  const [commentText, setCommentText] = useState('');

  const { createComment, isCreating: isCreatingComment } = useComments(postId);

  const handleAddComment = useCallback(
    async (content: string) => {
      try {
        await createComment({
          comment: content,
          ...(parentCommentId && { parentCommentId }),
        });
        setCommentText('');
      } catch (error: any) {
        console.error('Failed to add comment:', error);
        if (!error?.message) {
          toast.error('Failed to add comment. Please try again.');
        }
        throw error;
      }
    },
    [createComment, parentCommentId]
  );

  const handleFormSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const trimmed = commentText.trim();
    if (!trimmed) return;

    await handleAddComment(trimmed);
  };

  return (
    <Box
      component="form"
      onSubmit={handleFormSubmit}
      display="flex"
      gap="12px"
      alignItems="flex-start"
      mt={2}
      sx={{
        ...sx,
      }}
    >
      <Avatar
        src="/placeholder-avatar.png"
        alt="Your profile"
        sx={{ width: 40, height: 40 }}
      />
      <TextField
        fullWidth
        size="small"
        value={commentText}
        onChange={(e) => setCommentText(e.target.value)}
        placeholder="What do you think of this post?"
        InputProps={{
          sx: {
            backgroundColor: '#F3F4F6',
            borderRadius: '8px',
            height: '45px',
            px: 2,
            fontSize: '14px',
          },
        }}
      />
      <IconButton
        type="submit"
        disabled={!commentText.trim() || isCreatingComment}
        sx={{
          alignSelf: 'center',
          color: 'secondary.main',
          '&:disabled': {
            color: 'text.disabled',
          },
        }}
      >
        <SendIcon />
      </IconButton>
    </Box>
  );
};

export default AddCommentInput;
