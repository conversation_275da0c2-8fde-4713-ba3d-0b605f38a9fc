import { useTheme } from '@emotion/react';
import { Box, SxProps, Typography, useMediaQuery } from '@mui/material';

interface OrDividerProps {
  text?: string;
  sx?: SxProps;
}
/**
 * OrDivider component for separating form and social login options
 */
export const OrDivider = ({ text = 'OR', sx }: OrDividerProps) => {
  const theme: any = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%',
        my: { xs: '32px', sm: '44px' },
        ...(sx || {}),
      }}
    >
      <Typography
        sx={{
          color: 'neutral.500',
          fontWeight: 500,
          fontSize: '16px',
        }}
      >
        {isSmallScreen ? 'OR sign in with' : 'OR'}
      </Typography>
    </Box>
  );
};

export default OrDivider;
