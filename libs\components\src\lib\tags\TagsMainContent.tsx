'use client';

import { useSearchParams } from 'next/navigation';
import { Box } from '@mui/material';
import TagSection from './TagSection';
import FullTagSectionView from './FullTagSectionView';
import { useTranslations } from 'next-intl';

const allYourTags = ['Surgery', 'AI', 'Heart Valve', 'Stent'];
const allSuggestedTags = [
  'Cardiology',
  'React',
  'Medical Imaging',
  'Cardio AI',
];

export default function TagsMainContent() {
  const searchParams = useSearchParams();
  const tagType = searchParams?.get('tagType');
  const t = useTranslations('tagsPage');

  if (tagType === 'your-tags' || tagType === 'suggested') {
    return (
      <Box
        sx={{
          maxWidth: '976px',
          width: '100%',
          px: { xs: '16px', smd: '0px' },
        }}
      >
        <FullTagSectionView tagType={tagType} />
      </Box>
    );
  }

  // Default view: show both with 2 tags each
  return (
    <Box
      sx={{
        maxWidth: '976px',
        width: '100%',
        px: { xs: '16px', smd: '0px' },
      }}
    >
      <TagSection
        title={t('yourTags')}
        tags={allYourTags.slice(0, 2)}
        tagType="your-tags"
        showAll={false}
        hideSeeAll={false}
      />
      <TagSection
        title={t('suggestedTags')}
        tags={allSuggestedTags.slice(0, 2)}
        tagType="suggested"
        showAll={false}
        hideSeeAll={false}
      />
    </Box>
  );
}
