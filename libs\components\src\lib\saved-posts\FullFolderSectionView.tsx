'use client';

import { Box } from '@mui/material';
import FolderBox from './FolderBox';

const FOLDERS = [
  'All Saved',
  'Important Articles',
  'Surgery',
  'Saved',
  'Case Studies',
  'Weekly Notes',
  'Clinical Trials',
  'AI Research',
  'New Procedures',
  'Heart Valve',
];

export default function FullFolderSectionView() {
  return (
    <Box
      sx={{
        display: 'flex',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
        gap: { xs: '20px', sm: '40px' },
        maxWidth: '976px',
        width: '100%',
        px: { xs: '16px', smd: '0px' },
      }}
    >
      {FOLDERS.map((folder) => (
        <Box
          key={folder}
          sx={{
            flex: '1 1 45%',
            minWidth: '300px',
            maxWidth: '100%',
          }}
        >
          <FolderBox
            name={folder}
            sx={{
              backgroundColor: { xs: '#F8F9FA', sm: 'white' },
              width: '100%',
            }}
          />
        </Box>
      ))}
    </Box>
  );
}
