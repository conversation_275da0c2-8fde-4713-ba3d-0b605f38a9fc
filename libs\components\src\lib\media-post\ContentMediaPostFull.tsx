'use client';

import { useState } from 'react';
import { Box, IconButton } from '@mui/material';
import { ChevronLeft, ChevronRight } from '@mui/icons-material';
import { ContentMediaText } from './ContentMediaText';
import { getCdnUrl } from '@minicardiac-client/utilities';

type Media = {
  id: string;
  mediaPath: string;
  mediaType: string;
  order: number;
  altText?: string;
  thumbnailPath?: string;
  width?: number;
  height?: number;
};

export const ContentMediaPostFull = ({
  content,
  media,
}: {
  content: string;
  media: Media[];
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const handlePrev = () => {
    setCurrentIndex((prev) => Math.max(prev - 1, 0));
  };

  const handleNext = () => {
    setCurrentIndex((prev) => Math.min(prev + 1, media.length - 1));
  };

  return (
    <Box display="flex" flexDirection="column" alignItems="center" gap={2}>
      {/* Carousel */}
      <Box
        position="relative"
        width="100%"
        maxWidth="824px"
        height="360px"
        overflow="hidden"
        borderRadius="12px"
      >
        {/* Images */}
        <Box
          display="flex"
          height="100%"
          sx={{
            transform: `translateX(-${currentIndex * 100}%)`,
            transition: 'transform 0.5s ease',
            width: `100%`,
            backgroundColor: '#fff',
          }}
        >
          {media.map((m, index) => (
            <Box
              key={index}
              flex="0 0 100%"
              height="100%"
              display="flex"
              justifyContent="center"
              alignItems="center"
            >
              <Box
                component="img"
                src={getCdnUrl(m.mediaPath)}
                alt={m.altText ?? ''}
                sx={{
                  maxWidth: '553px',
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                  objectPosition: 'center',
                  borderRadius: '12px',
                }}
              />
            </Box>
          ))}
        </Box>

        {/* Left Arrow */}
        {currentIndex > 0 && (
          <IconButton
            onClick={handlePrev}
            sx={{
              position: 'absolute',
              top: '50%',
              left: '8px',
              transform: 'translateY(-50%)',
              width: 36,
              height: 36,
              color: '#A24295',
              backgroundColor: 'transparent',
              '&:hover': { backgroundColor: 'transparent' },
            }}
          >
            <ChevronLeft fontSize="large" />
          </IconButton>
        )}

        {/* Right Arrow */}
        {currentIndex < media.length - 1 && (
          <IconButton
            onClick={handleNext}
            sx={{
              position: 'absolute',
              top: '50%',
              right: '8px',
              transform: 'translateY(-50%)',
              width: 36,
              height: 36,
              color: '#A24295',
              backgroundColor: 'transparent',
              '&:hover': { backgroundColor: 'transparent' },
            }}
          >
            <ChevronRight fontSize="large" />
          </IconButton>
        )}
      </Box>

      {/* Text */}
      <ContentMediaText content={content} />
    </Box>
  );
};
