'use client';

import {
  Box,
  Button,
  Dialog,
  DialogContent,
  IconButton,
  Typography,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

interface DragAndDropDialogProps {
  open: boolean;
  onClose: () => void;
  folderName: string;
  onMove: () => void;
  onCopy: () => void;
}

export default function DragAndDropDialog({
  open,
  onClose,
  folderName,
  onMove,
  onCopy,
}: DragAndDropDialogProps) {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          width: '100%',
          maxWidth: '596px',
          borderRadius: '12px',
          backgroundColor: '#fff',
          padding: '20px',
        },
      }}
    >
      <DialogContent sx={{ padding: 0 }}>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            height: '100%',
            gap: '24px',
          }}
        >
          {/* Header */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <Typography
              fontSize="24px"
              fontWeight={500}
              color="#1E1E1E"
              fontFamily="Plus Jakarta Sans"
            >
              Copy or Move post?
            </Typography>
            <IconButton onClick={onClose}>
              <CloseIcon sx={{ color: '#A24295' }} />
            </IconButton>
          </Box>

          {/* Message */}
          <Typography
            sx={{
              fontSize: '16px',
              fontWeight: 400,
              color: '#737678',
              lineHeight: '24px',
              maxWidth: '100%',
            }}
          >
            Would you like to copy or move this post to “{folderName}”?
          </Typography>

          {/* Footer Buttons */}
          <Box display="flex" justifyContent="center" gap="16px">
            <Button
              variant="outlined"
              onClick={onMove}
              sx={{
                width: '135px',
                height: '40px',
                fontWeight: 700,
                borderColor: '#A24295',
                color: '#A24295',
                '&:hover': {
                  backgroundColor: '#f9f0f5',
                },
              }}
            >
              Move
            </Button>
            <Button
              variant="outlined"
              onClick={onCopy}
              sx={{
                width: '135px',
                height: '40px',
                fontWeight: 700,
                borderColor: '#A24295',
                color: '#A24295',
                '&:hover': {
                  backgroundColor: '#f9f0f5',
                },
              }}
            >
              Copy
            </Button>
          </Box>
        </Box>
      </DialogContent>
    </Dialog>
  );
}
