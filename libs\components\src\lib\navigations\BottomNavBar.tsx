import { Box, Stack } from '@mui/material';
import { useState } from 'react';
import HomeIcon from '../Icons/HomeIcon';
import NetworkIcon from '../Icons/NetworkIcon';
import OpportunityIcon from '../Icons/OpportunityIcon';
import WorkspaceIcon from '../Icons/WorkspaceIcon';
import { useRouter } from 'next/navigation';
import MessageIcon from '../Icons/MessageIcon';

export const SideNavItems = [
  HomeIcon,
  NetworkIcon,
  MessageIcon,
  OpportunityIcon,
  WorkspaceIcon,
];

const routes = ['#', '#', '#', '#', '#'];

export default function BottomNavBar() {
  const [activeIndex, setActiveIndex] = useState(0);
  const router = useRouter();

  const handleNavClick = (index: number) => {
    setActiveIndex(index);
    router.push(routes[index]);
  };

  return (
    <Box
      sx={{
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 99,
        backgroundColor: 'white',
        px: 2,
        py: 1,
        boxShadow: '0px -4px 20px rgba(0, 0, 0, 0.1)',
        height: '72px',
      }}
    >
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        height="100%"
      >
        {SideNavItems.map((IconComponent, index) => {
          const isActive = activeIndex === index;
          const fillColor = isActive ? '#A24295' : '#A3A3A3';

          return (
            <Box
              key={index}
              onClick={() => handleNavClick(index)}
              sx={{
                borderRadius: '12px',
                cursor: 'pointer',
                bgcolor: isActive ? 'rgba(162, 66, 149, 0.1)' : 'transparent',
                transition: 'all 0.3s ease',
                '&:hover': {
                  bgcolor: 'rgba(162, 66, 149, 0.15)',
                },
                height: '36px',
                width: '36px',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <IconComponent fill={fillColor} />
            </Box>
          );
        })}
      </Stack>
    </Box>
  );
}
