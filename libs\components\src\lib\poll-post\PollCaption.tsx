'use client';

import { Box, Typography } from '@mui/material';
import { useRouter } from 'next/navigation';
import { FullPageLoader } from '../full-page-loader';
import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { generateSlug } from '@minicardiac-client/utilities';

const MAX_LINES = 2;

interface PollCaptionProps {
  POLL_CAPTION: string;
  postId?: string;
}

export const PollCaption = ({ POLL_CAPTION, postId }: PollCaptionProps) => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const t = useTranslations('pollPost');

  const handleSeeMoreClick = () => {
    const slug = generateSlug(POLL_CAPTION);
    setLoading(true);
    router.push(`/feed/poll/${postId}/${slug}`);
  };

  return (
    <>
      <FullPageLoader
        open={loading}
        message={t('loadingPoll')}
        sx={{ backgroundColor: '#1E1E1E40' }}
      />

      <Box sx={{ position: 'relative', width: '100%' }}>
        <Typography
          sx={{
            fontSize: '12px',
            lineHeight: '18px',
            fontWeight: 400,
            color: '#1E1E1E',
            display: '-webkit-box',
            WebkitLineClamp: MAX_LINES,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            userSelect: 'text',
          }}
        >
          {POLL_CAPTION}
        </Typography>

        <Typography
          sx={{
            mt: '4px',
            fontSize: '12px',
            lineHeight: '18px',
            fontWeight: 600,
            color: '#A24295',
            cursor: 'pointer',
            userSelect: 'none',
            display: 'inline-block',
          }}
          onClick={handleSeeMoreClick}
        >
          {t('seeMore')}
        </Typography>
      </Box>
    </>
  );
};
