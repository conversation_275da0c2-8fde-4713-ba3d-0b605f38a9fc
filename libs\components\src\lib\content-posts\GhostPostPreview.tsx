'use client';
import { Box } from '@mui/material';

import ContentQuestionPost from './ContentQuestionPost';
import ContentPollPost from './ContentPollPost';
import { ContentArticlePost } from './ContentArticlePost';
import ContentMediaPost from './ContentMediaPost';
import ContextTextPost from './ContentTextPost';
import { memo } from 'react';

type GhostPostPreviewProps = {
  postId: string;
  type: 'question' | 'poll' | 'article' | 'media' | 'text';
};

export const GhostPostPreview = memo(
  ({ postId, type }: GhostPostPreviewProps) => {
    const renderPreview = () => {
      switch (type) {
        case 'question':
          return <ContentQuestionPost postId={postId} ghost />;
        case 'poll':
          return <ContentPollPost postId={postId} ghost />;
        case 'article':
          return <ContentArticlePost postId={postId} ghost />;
        case 'media':
          return <ContentMediaPost postId={postId} ghost />;
        case 'text':
          return <ContextTextPost postId={postId} ghost />;
        default:
          return null;
      }
    };

    return (
      <Box
        sx={{
          width: '100%',
          maxWidth: '600px',
          p: 2,
          boxShadow: 6,
          background: 'white',
          borderRadius: '12px',
          opacity: 0.95,
          pointerEvents: 'none',
        }}
      >
        {renderPreview()}
      </Box>
    );
  }
);
