'use client';

import { Component, ReactNode } from 'react';
import { Box, Button, Typography } from '@mui/material';

type Props = {
  children: ReactNode;
  fallback?: ReactNode;
};

type State = {
  hasError: boolean;
};

class ErrorBoundary extends Component<Props, State> {
  override state: State = { hasError: false };

  static getDerivedStateFromError(_: Error): State {
    return { hasError: true };
  }

  override componentDidCatch(error: Error, info: React.ErrorInfo) {
    console.error('Error caught in ErrorBoundary:', error, info);
  }

  override render() {
    if (this.state.hasError) {
      return (
        this.props.fallback ?? (
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            padding={4}
            minHeight="50vh"
          >
            <Typography variant="h6" color="error" gutterBottom>
              Something went wrong.
            </Typography>
            <Button
              variant="contained"
              onClick={() => window.location.reload()}
              sx={{ backgroundColor: '#A24295', color: 'white', mt: 2 }}
            >
              Refresh
            </Button>
          </Box>
        )
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
