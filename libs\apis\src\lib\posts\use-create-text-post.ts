import { useMutation } from '@tanstack/react-query';
import { postApi, CreateTextPostRequest } from './post-api.js';
import { toast } from 'react-toastify';

interface ApiResponse {
  data: unknown;
  // Add other properties that might be in the response
}

interface UseCreateTextPostOptions {
  onSuccess?: (data: ApiResponse) => void;
  onError?: (error: Error) => void;
}

export function useCreateTextPost(options?: UseCreateTextPostOptions) {
  return useMutation({
    mutationFn: async (data: CreateTextPostRequest) => {
      try {
        const response = await postApi.createTextPost(data);
        return response;
      } catch (error) {
        console.error('Error creating text post:', error);
        throw error;
      }
    },
    onSuccess: (data: ApiResponse) => {
      toast.success('Post created successfully!');
      options?.onSuccess?.(data);
    },
    onError: (error: unknown) => {
      console.error('Error creating post:', error);
      const errorMessage = error && 
        typeof error === 'object' && 
        'response' in error && 
        error.response && 
        typeof error.response === 'object' && 
        'data' in error.response && 
        error.response.data && 
        typeof error.response.data === 'object' && 
        'message' in error.response.data && 
        typeof error.response.data.message === 'string'
          ? error.response.data.message
          : 'Failed to create post';
      toast.error(errorMessage);
      if (error instanceof Error) {
        options?.onError?.(error);
      } else {
        options?.onError?.(new Error(errorMessage));
      }
    },
  });
}
