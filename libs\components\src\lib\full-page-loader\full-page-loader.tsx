import React from 'react';
import { CircularProgress, Typography, Backdrop, SxProps } from '@mui/material';

interface FullPageLoaderProps {
  open: boolean;
  message?: string;
  sx?: SxProps;
}

export const FullPageLoader: React.FC<FullPageLoaderProps> = ({
  open,
  message = '', // Default to empty string to not show any text
  sx,
}) => {
  return (
    <Backdrop
      sx={{
        color: '#fff',
        zIndex: (theme) => theme.zIndex.drawer + 1,
        flexDirection: 'column',
        gap: 2,
        backgroundColor: '#FFFFFF',
        ...sx,
      }}
      open={open}
    >
      <CircularProgress color="secondary" />

      {message && message.length > 0 && (
        <Typography variant="h6" component="div">
          {message}
        </Typography>
      )}
    </Backdrop>
  );
};

export default FullPageLoader;
