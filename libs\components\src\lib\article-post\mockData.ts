import { Comment } from '@minicardiac-client/types';
import { MEDIA_1 } from '../auth';

export const mockComments: Comment[] = [
  {
    id: '1',
    user: {
      id: 'user-1',
      name: '<PERSON>',
      profilePic: '/placeholder-avatar.png',
    },
    postedAgo: '2h ago',
    comment: 'Great article! Learned a lot.',
    createdAt: new Date().toISOString(), // optional
    repliesCount: 0, // optional, you can omit if no replies
  },
];
export const otherArticles = [
  {
    id: '121',
    title: 'Innovations in Cardiology 2025',
    thumbnail: MEDIA_1,
  },
];
