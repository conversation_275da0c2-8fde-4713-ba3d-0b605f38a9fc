import { AnswerComment } from '../comments/comments.types.js';

export type FeedPostType = {
  id: string;
  title?: string;
  content: string;
  postedAt: string;
  isLiked: boolean;
  likesCount: number;
  commentsCount: number;
  repostCount: number;
  shareCount: number;
  publisherName?: string;
  profileImageUrlThumbnail?: string;
  postType: 'question' | 'poll' | 'article' | 'media' | 'text';
  featuredComment?: AnswerComment | null;
  coverImagePath?: string;
  postMedias: Array<{
    id: string;
    mediaPath: string;
    mediaType: string;
    order: number;
    altText?: string;
    thumbnailPath?: string;
    width?: number;
    height?: number;
  }>;
  tags?: string[];
};

export interface FeedSearchState {
  searchKeyword: string;
  setSearchKeyword: (keyword: string) => void;
  postTypes: string[];
  setPostTypes: (types: string[]) => void;
}

export interface FeedState {
  feed: FeedPostType[];
  setFeed: (feed: FeedPostType[]) => void;
  updateLike: (postId: string, isLiked: boolean) => void;
}
