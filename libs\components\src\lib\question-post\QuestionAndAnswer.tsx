'use client';

import { Box } from '@mui/material';
import { useTranslations } from 'next-intl';

import QuestionAndAnswerIcon from '../Icons/PostIcons/QuestionAndAnswerIcon';
import QuestionPostContent from './QuestionPostContent';
import NoAnswerBox from './NoAnswerBox';
import AnswerPostContent from './AnswerPostContent';
import { AnswerComment } from '@minicardiac-client/types';

export const QuestionAndAnswer = ({
  pinnedAnswer,
  content,
  postId,
}: {
  pinnedAnswer: AnswerComment | null;
  content: string;
  postId: string;
}) => {
  const t = useTranslations('questionPost');

  return (
    <>
      <Box display={'flex'} width={'100%'} gap={'16px'}>
        <QuestionAndAnswerIcon
          questionFill={'#A24295'}
          answerFill={'#A3A3A3'}
        />
        <QuestionPostContent
          html={content}
          questionId={postId}
          questionTitle={content}
        />
      </Box>

      <Box display={'flex'} width={'100%'} gap={'16px'}>
        <QuestionAndAnswerIcon />
        {pinnedAnswer && pinnedAnswer.isPinned ? (
          <AnswerPostContent comment={pinnedAnswer} questionId={postId} />
        ) : (
          <NoAnswerBox
            title={t('noAnswerTitle')}
            subtitle={t('noAnswerSubtitle')}
          />
        )}
      </Box>
    </>
  );
};
