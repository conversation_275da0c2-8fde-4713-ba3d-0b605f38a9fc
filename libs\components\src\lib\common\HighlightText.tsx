import React from 'react';

type HighlightTextProps = {
  text: string;
  keyword: string;
  style?: React.CSSProperties;
};

const HighlightText: React.FC<HighlightTextProps> = ({
  text,
  keyword,
  style,
}) => {
  if (!keyword) return <>{text}</>;
  const regex = new RegExp(
    `(${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`,
    'gi'
  );
  const parts = text.split(regex);

  return (
    <>
      {parts.map((part, i) =>
        regex.test(part) ? (
          <b key={i} style={style}>
            {part}
          </b>
        ) : (
          <span key={i}>{part}</span>
        )
      )}
    </>
  );
};

export default HighlightText;
