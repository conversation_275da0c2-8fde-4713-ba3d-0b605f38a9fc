import React, { memo } from 'react';  // Added React import here
import { Box, Typography } from '@mui/material';

interface ActionButtonWithCountProps {
  icon: React.ReactNode;
  label: string;
  count: number;
  onClick: (e: React.MouseEvent) => void;
}

const ActionButtonWithCount = memo(
  ({ icon, label, count, onClick }: ActionButtonWithCountProps) => {
    const handleClick = (e: React.MouseEvent) => {
      e.stopPropagation();
      e.preventDefault();
      onClick(e);
    };
    
    // Rest of the component remains the same
    const iconWithClickHandler = React.isValidElement(icon)
      ? React.cloneElement(icon, {
          onClick: (e: React.MouseEvent) => {
            e.stopPropagation();
            handleClick(e);
          }
        } as React.HTMLAttributes<HTMLElement>)
      : icon;

    return (
      <Box
        component="div"
        display="flex"
        alignItems="center"
        justifyContent="center"
        gap="4px"
        sx={{ 
          cursor: 'pointer',
          '& > *': {
            pointerEvents: 'none' // Prevent nested elements from capturing clicks
          }
        }}
        onClick={handleClick}
      >
        {iconWithClickHandler}
        <Typography
          fontSize="12px"
          fontWeight={600}
          color="#A24295"
          sx={{ 
            display: { xs: 'none', sm: 'inline' },
            pointerEvents: 'none' // Prevent text from capturing clicks
          }}
        >
          {label} ({count})
        </Typography>
        <Typography
          fontSize="12px"
          fontWeight={600}
          color="#A24295"
          sx={{ 
            display: { xs: 'inline', sm: 'none' },
            pointerEvents: 'none' // Prevent text from capturing clicks
          }}
        >
          {count}
        </Typography>
      </Box>
    );
  }
);

export default ActionButtonWithCount;