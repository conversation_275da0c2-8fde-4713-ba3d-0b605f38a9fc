import { getCdnUrl } from '@minicardiac-client/utilities';
import { SignupOption } from '../types/auth.types';

/**
 * Carousel images for auth pages
 */

export const SLIDES = [
  {
    id: 0,
    bgImage: getCdnUrl('assets/carousel-images/slide1.jpg'),
    foregroundImage: getCdnUrl('assets/carousel-images/foregroundSlide1.png'),
    title: 'MiniCardiac',
    subtitle: 'Discover and connect with cardiologists',
    description:
      'cardio surgeons, hospitals, and more. Follow up on the latest in healthcare research to stay informed and healthy!',
    position: '50% 4.5%',
  },
  {
    id: 1,
    bgImage: getCdnUrl('assets/carousel-images/slide2.jpg'),
    foregroundImage: getCdnUrl('assets/carousel-images/foregroundSlide2.png'),
    title: 'MiniCardiac',
    subtitle: 'Establish your professional presence',
    description:
      'build your network, find jobs and opportunities, and collaborate with colleagues!',
    position: '50% 5%',
  },
  {
    id: 2,
    bgImage: getCdnUrl('assets/carousel-images/slide3.jpg'),
    foregroundImage: getCdnUrl('assets/carousel-images/foregroundSlide3.png'),
    title: 'MiniCardiac',
    subtitle: 'Connect with professionals',
    description:
      'and other organisations to build your professional network, with sponsorships and increased opportunities.',
    position: '50% 5%',
  },
];

/**
 * Signup options for different user types
 */
export const SIGNUP_OPTIONS: SignupOption[] = [
  {
    title: 'Professional',
    description:
      'For those who work in and around the cardiac healthcare ecosystem. Medical students are more than welcome too!',
    icon: getCdnUrl('assets/icons/professional.png'),
    path: '/signup/professional',
    character: getCdnUrl('assets/woman-doctor-Character.png'),
    shape: getCdnUrl('assets/shape.png'),
  },
  {
    title: 'Organisation',
    description:
      'Industry, device manufacturers, hospitals, societies, insurance providers, non-profits, and more!',
    icon: getCdnUrl('assets/icons/organization.png'),
    path: '/signup/organization',
    character: getCdnUrl('assets/gentle-man-Charecter.png'),
    shape: getCdnUrl('assets/shape.png'),
  },
  {
    title: 'Public',
    description: 'For patients, families, and the scientifically curious.',
    icon: getCdnUrl('assets/icons/public.png'),
    path: '/signup/patient',
    character: getCdnUrl('assets/girl-man-charecter.png'),
    shape: getCdnUrl('assets/shape.png'),
  },
];

/**
 * Auth routes
 */
export const AUTH_ROUTES = {
  SIGNIN: '/signin',
  SIGNUP_TYPE: '/signup-type',
  SIGNUP_PROFESSIONAL: '/signup/professional',
  SIGNUP_ORGANIZATION: '/signup/organization',
  // SIGNUP_PUBLIC: '/signup/public',};
};

/**
 * Social login providers
 */
export const SOCIAL_PROVIDERS = {
  GOOGLE: {
    name: 'Google',
    icon: getCdnUrl('assets/icons/search.png'),
  },
  APPLE: {
    name: 'Apple',
    icon: getCdnUrl('assets/icons/apple-logo.png'),
  },
};

export const BACKGROUND_GRADIENT_IMAGE = getCdnUrl(
  'assets/gradient-overlay.jpg'
);

export const BLANK_PROFILE = getCdnUrl('assets/new-back-image-for-file.png');
export const PATIENT_WELCOME_PIC = getCdnUrl('assets/patient-welcome-card.svg');
export const MINICARDIAC_ICON = getCdnUrl('/assets/icons/minicardiac-icon.svg');

export const SideNavbarIcons = [
  '/assets/icons/mail.svg',
  '/assets/icons/bell.svg',
  '/assets/icons/settings.svg',
];

export const PRESTIGE_ICON = getCdnUrl('/assets/icons/Prestige-icon.svg');
export const PRESTIGE_ICON_FULL = getCdnUrl(
  '/assets/icons/Prestige-icon-full.svg'
);

export const ORAGANIZATION_WELCOME_PIC = getCdnUrl(
  '/assets/signup-organization-image.svg'
);

export const MINICARDIAC_TRADEMARK = getCdnUrl(
  '/assets/icons/minicardiac-trademark.svg'
);
export const SEARCH_ICON = getCdnUrl('/assets/icons/search-icon.svg');
export const BOOKMARK_SAVED_POSTS = getCdnUrl(
  '/assets/icons/bookmark-save-post.png'
);

export const FILTER_ICON = getCdnUrl('/assets/icons/filter.svg');
export const TAGS_ICON = getCdnUrl('/assets/icons/tags.svg');
export const BOOKMARK = getCdnUrl('/assets/icons/bookmark.svg');
export const FOLDER_ICON = getCdnUrl('/assets/icons/folder-icon.svg');

export const MEDIA_1 = getCdnUrl('/assets/media-post-images/media-post-1.jpg');
export const MEDIA_2 = getCdnUrl('/assets/media-post-images/media-post-2.jpg');
export const MEDIA_3 = getCdnUrl('/assets/media-post-images/media-post-3.jpg');
export const MEDIA_4 = getCdnUrl('/assets/media-post-images/media-post-4.jpg');

export const MEDIA_POSTS = [MEDIA_1, MEDIA_2, MEDIA_3, MEDIA_4];
