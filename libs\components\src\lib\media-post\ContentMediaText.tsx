import { Typography } from '@mui/material';

export const ContentMediaText = ({ content }: { content: string }) => {
  return (
    <Typography
      component="div"
      sx={{
        fontSize: '12px',
        lineHeight: '18px',
        color: '#1E1E1E',
        display: 'block',

        WebkitBoxOrient: 'vertical',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        userSelect: 'text',
      }}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
};
