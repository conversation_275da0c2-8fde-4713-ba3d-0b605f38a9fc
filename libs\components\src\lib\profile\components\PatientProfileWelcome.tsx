import { Box, Stack, Typography, useTheme } from '@mui/material';
import { ExtendedTheme } from '../../auth/types/auth.types';

interface PatientProfileWelcomeProps {
  title?: string;
  patientName: string;
  subtitle?: string;
}

export const PatientProfileWelcome = ({
  title = 'Welcome to MiniCardiac',
  patientName,
  subtitle = '',
}: PatientProfileWelcomeProps) => {
  const theme = useTheme() as ExtendedTheme;

  return (
    <Stack
      sx={{
        width: '100%',
        alignItems: 'center',
        position: 'relative',
        mt: { xs: '16px', sm: '2.75rem', xxl: '48px' },
      }}
      gap={{ xs: '24px', sm: '14px' }}
    >
      {/* Welcome message */}
      <Typography
        variant="h3"
        sx={{
          display: { xs: 'flex', smd: 'block' },
          flexDirection: { xs: 'column' },
          fontWeight: { xs: 300, smd: 500, xxl: 500 },
          fontSize: { xs: '16px', smd: '28px', xxl: '32px' },
          lineHeight: '100%',
          letterSpacing: '0%',
          textAlign: 'center',
          color: (theme.palette as any).neutral?.[900] || '#1E1E1E',
        }}
      >
        {title}{patientName ? ',' : ''}{' '}
        <Box
          component="span"
          sx={{
            fontWeight: 500,
            fontSize: { xs: '20px', smd: '28px', xxl: '34px' },
            mt: '1px',
          }}
        >
          {capitalizeWords(patientName)}!
        </Box>
      </Typography>

      {/* Subtitle text */}
      {subtitle && (
        <Typography
          variant="body1"
          sx={{
            fontWeight: 400,
            fontSize: { xs: '12px', smd: '16px', xxl: '18px' },
            lineHeight: '100%',
            letterSpacing: '0%',
            textAlign: 'center',
            color: (theme.palette as any).neutral?.[600] || '#737678',
          }}
        >
          {subtitle}
        </Typography>
      )}
    </Stack>
  );
};

export default PatientProfileWelcome;

const capitalizeWords = (name: string) =>
  name.replace(/\b\w/g, (char) => char.toUpperCase());
