import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Stack from '@mui/material/Stack';

import Image from 'next/image';
import { MINICARDIAC_ICON, MINICARDIAC_TRADEMARK } from '../auth';
import NavItem from './NavItem';
import HomeIcon from '../Icons/HomeIcon';
import MessageIcon from '../Icons/MessageIcon';
import NotificationIcon from '../Icons/NotificationIcon';
import OpportunityIcon from '../Icons/OpportunityIcon';
import NetworkIcon from '../Icons/NetworkIcon';
import SettingIcon from '../Icons/SettingIcon';
import WorkspaceIcon from '../Icons/WorkspaceIcon';
import { useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';

import { getDecodedToken } from '@minicardiac-client/utilities';
import { useAuth } from '@minicardiac-client/apis';

import { FeedProfile } from './FeedProfile';
import { useTheme } from '@emotion/react';
import { useMediaQuery } from '@mui/material';
import { getOwnProfile } from '@minicardiac-client/apis';

const SideNavbarIcons = [MessageIcon, NotificationIcon, SettingIcon];
export const SideNavItems = [
  {
    icon: HomeIcon,
    label: 'Home',
    link: '/feed',
  },
  {
    icon: NetworkIcon,
    label: 'Network',
    link: '#',
  },
  {
    icon: OpportunityIcon,
    label: 'Opportunities',
    link: '#',
  },
  {
    icon: WorkspaceIcon,
    label: 'My Workspace',
    link: '#',
  },
];

export default function Sidebar() {
  const [userDetails, setUserDetails] = useState<{
    name: string;
    photoURL: string;
  } | null>(null);
  const { authState } = useAuth();

  const { data: profileDetails } = useQuery({
    queryKey: ['userProfile'],
    queryFn: getOwnProfile,
    enabled: !!authState?.isAuthenticated,
  });

  const theme: any = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('md'));

  useEffect(() => {
    async function fetchUserDetails() {
      const decodedToken = await getDecodedToken();
      setUserDetails({
        name: decodedToken.name,
        photoURL: profileDetails?.profileImageUrlThumbnail || '',
      });
    }

    fetchUserDetails();
  }, [authState, profileDetails?.profileImageUrlThumbnail]);

  return (
    <Stack
      direction="row"
      sx={{
        overflow: 'hidden',
        maxWidth: '100%',
        flexDirection: { sm: 'column', xs: 'column', md: 'row' },
        gap: { sm: '10px', xs: '10px', md: '20px' },
      }}
    >
      <Box
        sx={{
          height: '100vh',
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          position: 'relative',
          pt: '40px',
        }}
      >
        {/* Top Logo and Name */}
        <Box
          sx={{
            textAlign: 'center',
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
          }}
        >
          <Image
            src={isSmallScreen ? MINICARDIAC_ICON : MINICARDIAC_TRADEMARK}
            alt={'Mini Card Icon'}
            width={144}
            height={23}
          />
        </Box>

        {/* Profile */}
        <FeedProfile userDetails={userDetails} />
        {/* Notification Icons */}
        <Stack
          direction="row"
          mt={'28px'}
          display={'flex'}
          sx={{
            flexDirection: { sm: 'column', xs: 'column', md: 'row' },
            gap: { sm: '10px', xs: '10px', md: '20px' },
          }}
        >
          {SideNavbarIcons.map((IconComponent, index) => {
            return (
              <Box
                key={index}
                sx={{
                  width: 32,
                  height: 32,
                  borderRadius: '50%',
                  backgroundColor: 'rgba(162, 66, 149, 0.15)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: 'pointer',
                  transition: 'transform 0.2s ease-in-out',
                  '&:hover': {
                    transform: 'scale(1.1)',
                  },
                }}
              >
                <IconComponent fill={'#A24295'} size={16} />
              </Box>
            );
          })}
        </Stack>

        {/* Navigation Items */}
        <Stack gap={'4px'} sx={{ mt: '28px' }}>
          {SideNavItems.map((item, index) => (
            <NavItem
              key={index}
              icon={<item.icon />}
              label={item.label}
              link={item.link}
            />
          ))}
        </Stack>
        {/* Bottom About Text */}
        <Box
          sx={{
            textAlign: 'center',
            mt: 'auto',
            mb: 3,
            px: 2,
          }}
        >
          <Typography
            variant="caption"
            sx={{
              fontSize: 12,
              fontWeight: 600,
              color: '#A24295',
              cursor: 'pointer',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                color: '#831f74', // darker shade for hover
                transform: 'scale(1.05)',
              },
            }}
          >
            About MiniCardiac
          </Typography>

          <Typography
            variant="caption"
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              gap: '8px',
              fontSize: 12,
              fontWeight: 400,
              color: '#6E6E6E', // slightly lighter than default text
              mt: 1,
              flexWrap: 'wrap',
            }}
          >
            <Box
              component="span"
              sx={{
                cursor: 'pointer',
                transition: 'color 0.2s ease-in-out',
                '&:hover': {
                  color: '#A24295',
                },
              }}
            >
              Contact Us
            </Box>
            |
            <Box
              component="span"
              sx={{
                cursor: 'pointer',
                transition: 'color 0.2s ease-in-out',
                '&:hover': {
                  color: '#A24295',
                },
              }}
            >
              Legal
            </Box>
            |
            <Box
              component="span"
              sx={{
                cursor: 'pointer',
                transition: 'color 0.2s ease-in-out',
                '&:hover': {
                  color: '#A24295',
                },
              }}
            >
              Cookie Policy
            </Box>
          </Typography>
        </Box>
      </Box>
    </Stack>
  );
}
