import { useTranslations } from 'next-intl';
import { Helmet } from 'react-helmet-async';

function sanitizeAndTruncateTitle(title: string, maxLength = 100): string {
  const plainText = title.replace(/<[^>]+>/g, '').trim();

  // Truncate if needed
  if (plainText.length <= maxLength) return plainText;
  return plainText.slice(0, maxLength).replace(/\s+\S*$/, '') + '...';
}

const Head = ({ title = '' }: { title: string }) => {
  const t = useTranslations('headers');
  const finalTitle = sanitizeAndTruncateTitle(title, 100);

  return (
    <Helmet>
      <title>{`${finalTitle} | ${t('minicardiac')}`}</title>
    </Helmet>
  );
};

export default Head;
