import { OtherPostProps } from '@minicardiac-client/types';
import { Box, Typography } from '@mui/material';

const ArticleCard = ({ article }: { article: OtherPostProps }) => (
  <Box
    bgcolor="#fff"
    borderRadius="12px"
    width="100%"
    display="flex"
    flexDirection="column"
    gap="8px"
  >
    <Box
      component="img"
      src={article.thumbnail}
      alt={article.title}
      sx={{
        width: '100%',
        height: '107px',
        objectFit: 'cover',
        borderRadius: '8px',
      }}
    />
    <Typography fontSize="17px" fontWeight={400} color="#333537">
      {article.title}
    </Typography>
  </Box>
);

export default ArticleCard;
