'use client';

import { Box, Menu, MenuItem, Typography, useMediaQuery } from '@mui/material';
import { Iconify } from '../iconify';
import { useState } from 'react';
import { Profile } from './Profile';
import {
  useAuth,
  usePostDialogStore,
  useSignOut,
} from '@minicardiac-client/apis';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useTheme } from '@emotion/react';

export const FeedProfile = ({
  userDetails,
}: {
  userDetails: {
    name: string;
    photoURL: string;
  } | null;
}) => {
  const { authState } = useAuth();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const router = useRouter();
  const { setActiveDialog } = usePostDialogStore();

  const theme: any = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const { mutate: signOut } = useSignOut();

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const t = useTranslations('feedNavigation');

  const handleClose = async () => {
    setAnchorEl(null);
  };

  const handleSignout = async () => {
    setAnchorEl(null);
    try {
      await signOut(undefined, {
        onSuccess: () => {
          router.push('/signin');
        },
      });
    } catch (error) {
      console.error('Error during sign out:', error);
      router.push('/signin');
    }
  };

  const handleUnauthClick = () => {
    router.push('/signin');
  };

  const otherProfiles = [
    {
      id: 1,
      name: 'St. Andrews Hospital',
      photoURL: 'assets/carousel-images/slide1.jpg',
    },
    {
      id: 2,
      name: 'Morgan Abdi',
      photoURL: 'assets/carousel-images/slide2.jpg',
    },
    {
      id: 3,
      name: 'Emily Zhang',
      photoURL: '',
    },
    {
      id: 4,
      name: 'Mayur Hanwate',
      photoURL: '',
    },
  ];

  return (
    <Box
      sx={{
        textAlign: 'center',
        mt: { xs: 0, sm: '29px' },
        display: 'flex',
        flexDirection: 'column',
        gap: '20px',
      }}
    >
      <Box
        onClick={authState.isAuthenticated ? handleClick : handleUnauthClick}
        sx={{ cursor: 'pointer' }}
      >
        <Profile
          photoURL={userDetails?.photoURL}
          authenticated={authState.isAuthenticated}
        />
      </Box>

      <Box
        onClick={authState.isAuthenticated ? handleClick : handleUnauthClick}
        sx={{
          display: { xs: 'none', sm: 'flex' },
          gap: { sm: '2px', xs: '2px', md: '4px' },
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          transition: authState.isAuthenticated
            ? 'transform 0.2s ease-in-out'
            : 'none',
          '&:hover': authState.isAuthenticated
            ? { transform: 'scale(1.05)' }
            : {},
          flexDirection: { sm: 'column', xs: 'column', md: 'row' },
          textAlign: 'center',
          height: '20px',
        }}
      >
        <Typography
          sx={{
            fontFamily: 'Plus Jakarta Sans',
            fontWeight: 700,
            fontSize: { sm: '10px', xs: '10px', md: '16px' },
            color: '#A24295',
          }}
        >
          {authState.isAuthenticated ? userDetails?.name : 'Sign In / Sign Up'}
        </Typography>

        {authState.isAuthenticated && (
          <Box
            sx={{
              transition: 'transform 0.3s ease',
              transform: open ? 'rotate(90deg)' : 'rotate(0deg)',
            }}
          >
            <Iconify icon="material-symbols:chevron-right" color="#A24295" />
          </Box>
        )}
      </Box>

      {/* Dropdown Menu */}
      {!isMobile && authState.isAuthenticated && (
        <Menu
          anchorEl={anchorEl}
          id="account-menu"
          open={open}
          onClose={handleClose}
          onClick={handleClose}
          slotProps={{
            paper: {
              elevation: 0,
              sx: {
                width: '320px',
              },
            },
          }}
          anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'left' }}
        >
          {/* Header */}
          <Typography
            variant="body2"
            sx={{
              fontWeight: 600,
              fontSize: '12px',
              color: '#737678',
              px: '16px',
              py: '8px',
            }}
          >
            Profiles
          </Typography>

          {/* Profiles with vertical divider */}
          <Box
            sx={{
              position: 'relative',
              display: 'flex',
              // justifyContent: 'center',
              width: '100%',
            }}
          >
            {/* Fixed vertical line */}
            <Box
              sx={{
                position: 'absolute',
                left: '32px',
                top: 0,
                bottom: 0,
                width: '1px',
                backgroundColor: '#A3A3A3',
                borderRadius: '1.5px',
              }}
            />

            {/* Profile List */}
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: '8px',
                width: '100%',
              }}
            >
              {otherProfiles.slice(0, 3).map((profile) => (
                <Box
                  key={profile.id}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    py: '8px',
                    cursor: 'pointer',
                    ':hover': { backgroundColor: '#F6ECF4' },
                    height: '40px',
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      ml: '52px',
                      gap: '8px',
                    }}
                  >
                    <Profile
                      size={24}
                      photoURL={profile?.photoURL}
                      displayName={profile?.name}
                      sx={{ mx: '0px' }}
                    />
                    <Typography sx={{ fontSize: '16px', fontWeight: 400 }}>
                      {profile.name}
                    </Typography>
                  </Box>
                </Box>
              ))}

              {/* All Profiles Link */}
              {otherProfiles.length > 3 && (
                <Box
                  sx={{
                    backgroundColor: '#F6ECF4',
                    px: '16px',
                    py: '8px',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    '&:hover': {
                      backgroundColor: '#f3e0ee',
                    },
                    height: '40px',
                  }}
                  onClick={() => {
                    setActiveDialog('AllProfile');
                  }}
                >
                  <Box
                    sx={{
                      position: 'absolute',
                      left: '31px',

                      bottom: 0,
                      width: '3px',
                      backgroundColor: '#A24295',
                      height: '40px',
                    }}
                  />
                  <Typography
                    sx={{
                      fontSize: '16px',
                      fontWeight: 400,
                      ml: '40px',
                    }}
                  >
                    All profiles →
                  </Typography>
                </Box>
              )}
            </Box>
          </Box>

          {/* Activity */}
          <Typography
            sx={{
              fontSize: '16px',
              fontWeight: 400,
              px: '16px',
              py: '10px',
              mt: '8px',
              height: '40px',
            }}
          >
            Activity
          </Typography>

          {/* Logout */}
          <MenuItem
            onClick={handleSignout}
            sx={{ color: '#A24295', py: '16px', height: '40px' }}
          >
            <Typography sx={{ fontSize: '16px', fontWeight: 600 }}>
              {t('logout')}
            </Typography>
          </MenuItem>
        </Menu>
      )}
    </Box>
  );
};
