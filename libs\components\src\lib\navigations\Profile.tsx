import { Box, SxProps, Typography } from '@mui/material';
import { BLANK_PROFILE } from '../auth';
import { getImageUrl, getInitials } from '@minicardiac-client/utilities';
import { useAuth } from '@minicardiac-client/apis';
import { useState } from 'react';

type ProfileProps = {
  displayName?: string;
  photoURL?: string;
  size?: number;
  authenticated?: boolean;
  sx?: SxProps;
};

export const Profile = ({
  displayName,
  photoURL,
  size,
  authenticated,
  sx,
}: ProfileProps) => {
  const authState = useAuth();
  const [imageError, setImageError] = useState(false);

  // Fallback to auth user values if props not provided
  const fallbackDisplayName =
    displayName ?? authState.authState.user?.displayName ?? '';

  let fallbackPhotoURL: string | null;
  if (photoURL) {
    fallbackPhotoURL = getImageUrl(photoURL);
  } else {
    fallbackPhotoURL = authState.authState.user?.photoURL ?? null;
  }

  const width = size ?? { xs: 40, smd: 50 };
  const height = size ?? { xs: 40, smd: 50 };
  const fontSize = size ? size * 0.4 : { sm: '1rem', md: '1.2rem' };

  return (
    <Box
      sx={{
        width,
        height,
        borderRadius: '50%',
        backgroundColor: authenticated ? '#A2429514' : 'transparent',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative',
        overflow: 'hidden',
        mx: 'auto',
        cursor: 'pointer',
        ...sx,
      }}
    >
      {authenticated && (
        <Box
          component="img"
          src={BLANK_PROFILE}
          alt="Background pattern"
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            zIndex: 0,
            transform: 'scale(1.05)',
            transformOrigin: 'center',
          }}
        />
      )}

      <Box
        sx={{
          width,
          height,
          borderRadius: '50%',
          overflow: 'hidden',
          position: 'relative',
          cursor: 'pointer',
        }}
      >
        {fallbackPhotoURL && !imageError ? (
          <Box
            component="img"
            src={fallbackPhotoURL}
            alt="Profile Picture"
            onError={() => setImageError(true)}
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              zIndex: 1,
            }}
          />
        ) : (
          <>
            <Box
              component="img"
              src={BLANK_PROFILE}
              alt="Background pattern"
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                objectFit: 'cover',
                zIndex: 0,
                transform: 'scale(1.7)',
                transformOrigin: 'center',
              }}
            />
            <Box
              sx={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                zIndex: 1,
              }}
            >
              <Typography
                sx={{
                  fontSize,
                  fontWeight: 500,
                  color: (theme) =>
                    (theme.palette as any).neutral?.[900] || '#1E1E1E',
                }}
              >
                {getInitials(fallbackDisplayName)}
              </Typography>
            </Box>
          </>
        )}
      </Box>
    </Box>
  );
};
