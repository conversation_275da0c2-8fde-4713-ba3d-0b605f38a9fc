'use client';

import { Box, Typography, Divider, useMediaQuery } from '@mui/material';
import { Iconify } from '../../iconify';
import { useTheme } from '@emotion/react';
import LikeIcon from '../../Icons/ContentPostIcons/LikeIcon';
import { useCallback, useState } from 'react';
import { useLikePost } from '@minicardiac-client/apis';
import LikeIconSelected from '../../Icons/ContentPostIcons/LikeIconSelected';
import { useFeedStore } from '../../store/useFeedStore';

const PostActionsBar = ({
  commentsLength,
  likesCount,
  repostCount,
  shareCount,
  isLiked: initialLiked,
  postId,
}: {
  commentsLength: number;
  likesCount: number;
  repostCount: number;
  shareCount: number;
  isLiked: boolean;
  postId: string;
}) => {
  const theme: any = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));

  const likeMutation = useLikePost();

  const [isLiked, setIsLiked] = useState(initialLiked);
  const [likeCount, setLikeCount] = useState(likesCount);
  const [reposted, setReposted] = useState(false);
  const [repostCounter, setRepostCounter] = useState(repostCount);

  const handleLikeChange = useCallback(
    (postId: string, like: boolean) => {
      useFeedStore.getState().updateLike(postId, like);

      likeMutation.mutate(
        { postId, like },
        {
          onError: (error) => {
            console.error('Failed to like post:', error);
            useFeedStore.getState().updateLike(postId, !like);
            setIsLiked(!like);
            setLikeCount((prev) => prev + (like ? -1 : 1));
          },
        }
      );
    },
    [likeMutation]
  );

  const handleLikeClick = () => {
    const newLike = !isLiked;
    setIsLiked(newLike);
    setLikeCount((prev) => prev + (newLike ? 1 : -1));
    handleLikeChange(postId, newLike);
  };

  const handleRepostClick = () => {
    const newReposted = !reposted;
    setReposted(newReposted);
    setRepostCounter((prev) => prev + (newReposted ? 1 : -1));
  };

  return (
    <>
      <Divider sx={{ borderColor: '#A3A3A3', opacity: 0.5 }} />

      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: '20px',
        }}
      >
        <Box
          display={{ xs: 'none', sm: 'block' }}
          fontWeight={600}
          fontSize={12}
        >
          Comments({commentsLength})
        </Box>

        <Box
          display="flex"
          justifyContent={isSmallScreen ? 'space-between' : 'flex-end'}
          alignItems="center"
          gap={isSmallScreen ? 0 : '28px'}
          width={'100%'}
        >
          {isSmallScreen ? (
            <>
              <ActionButton
                icon={isLiked ? <LikeIconSelected /> : <LikeIcon />}
                label={`(${likeCount})`}
                onClick={handleLikeClick}
              />
              <ActionButton
                icon={
                  <Iconify
                    icon="ic:round-chat-bubble-outline"
                    sx={{ fontSize: 20, color: '#A24295' }}
                  />
                }
                label={`(${commentsLength})`}
              />
              <ActionButton
                icon={
                  <Iconify
                    icon="garden:arrow-retweet-stroke-12"
                    sx={{ fontSize: 20, color: '#A24295' }}
                  />
                }
                label={`(${repostCounter})`}
                onClick={handleRepostClick}
              />
              <ActionButton
                icon={
                  <Iconify
                    icon="mdi:share"
                    sx={{ fontSize: 20, color: '#A24295' }}
                  />
                }
                label={`(${shareCount})`}
              />
            </>
          ) : (
            <>
              <ActionButton
                icon={isLiked ? <LikeIconSelected /> : <LikeIcon />}
                label={`Like (${likeCount})`}
                onClick={handleLikeClick}
              />
              <ActionButton
                icon={
                  <Iconify
                    icon="garden:arrow-retweet-stroke-12"
                    sx={{ fontSize: 20, color: '#A24295' }}
                  />
                }
                label={`Repost (${repostCounter})`}
                onClick={handleRepostClick}
              />
              <ActionButton
                icon={
                  <Iconify
                    icon="mdi:share"
                    sx={{ fontSize: 20, color: '#A24295' }}
                  />
                }
                label={`Share (${shareCount})`}
              />
            </>
          )}
        </Box>
      </Box>
    </>
  );
};

const ActionButton = ({
  icon,
  label,
  onClick,
}: {
  icon: React.ReactNode;
  label: string;
  onClick?: () => void;
}) => (
  <Box
    display="flex"
    alignItems="center"
    gap="4px"
    sx={{ cursor: 'pointer', color: '#A24295' }}
    onClick={onClick}
  >
    {icon}
    <Typography fontSize="12px" fontWeight={600}>
      {label}
    </Typography>
  </Box>
);

export default PostActionsBar;
