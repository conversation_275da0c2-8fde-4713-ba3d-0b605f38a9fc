'use client';

import { Tabs, Tab } from '@mui/material';
import { FC } from 'react';

interface AuthTabsProps {
  value: number;
  onChange: (newValue: number) => void;
  signInLabel: string;
  signUpLabel: string;
}

const AuthTabs: FC<AuthTabsProps> = ({
  value,
  onChange,
  signInLabel,
  signUpLabel,
}) => {
  return (
    <Tabs
      value={value}
      onChange={(_, newValue) => onChange(newValue)}
      TabIndicatorProps={{
        style: { transition: 'all 0.4s ease' },
      }}
      sx={{
        width: {
          xs: '280px',
          sm: '320px',
          md: '320px',
          lg: '1/2',
        },
        margin: 'auto',
      }}
    >
      <Tab
        label={signInLabel}
        sx={(theme) => ({
          padding: 0,
          fontSize: { xs: '18px', sm: '15px', md: '16px' },
          fontWeight: 400,
          minWidth: 'unset',
          fontFamily: "'Plus Jakarta Sans', sans-serif",
          color: (theme.palette as any).neutral[600],
          '&.Mui-selected': {
            fontWeight: 500,
            color: theme.palette.secondary.main,
          },
        })}
      />
      <Tab
        label={signUpLabel}
        sx={(theme) => ({
          padding: 0,
          fontSize: { xs: '18px', sm: '15px', md: '16px' },
          fontWeight: 400,
          minWidth: 'unset',
          fontFamily: "'Plus Jakarta Sans', sans-serif",
          color: (theme.palette as any).neutral[600],
          '&.Mui-selected': {
            fontWeight: 500,
            color: theme.palette.secondary.main,
          },
        })}
      />
    </Tabs>
  );
};

export default AuthTabs;
