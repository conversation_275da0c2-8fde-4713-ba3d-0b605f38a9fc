import { useState } from 'react';

import {
  <PERSON>,
  <PERSON>Field,
  Typography,
  Menu,
  MenuItem,
  Stack,
  useMediaQuery,
} from '@mui/material';
import { LoadingButton } from '../loading-button';

import CustomDialog from './CustomDialog';
import CustomToggleButtonGroup from '../buttons/CustomToggleButtonGroup';
import { Iconify } from '../iconify';
import { PostButton } from '../buttons/PostButton';
import { toast } from 'react-toastify';
import { isContentEmpty } from '@minicardiac-client/utilities';
import { CustomEditorBase } from '../textEditor/CustomEditorBase';
import { BackButton } from '../buttons/Backbutton';
import { useTheme } from '@emotion/react';
import { useCreateTextPost } from '../../../../../libs/apis/src/lib/posts/use-create-text-post';

interface TextPostDialogProps {
  open: boolean;
  onClose: () => void;
  setOpenScheduleDialog: () => void;
  content: string;
  setContent: (content: string) => void;
  onPostCreated?: () => void;
}

const TextPostDialog = ({
  open,
  onClose,
  setOpenScheduleDialog,
  content,
  setContent,
  onPostCreated,
}: TextPostDialogProps) => {
  const [tags, setTags] = useState('');
  const [audience, setAudience] = useState<'PROFESSIONAL' | 'PUBLIC' | 'BOTH'>('PROFESSIONAL');
  const [speciality, setSpeciality] = useState<'CARDIAC_SURGEON' | 'CARDIOLOGIST' | 'BOTH'>('CARDIAC_SURGEON');

  const theme: any = useTheme();
  const screenBelowSM = useMediaQuery(theme.breakpoints.down('sm'));

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const openMenu = Boolean(anchorEl);

  const { mutate: createPost, isPending } = useCreateTextPost({
    onSuccess: () => {
      onClose();

      setContent('');

      if (onPostCreated) {
        console.log('Calling onPostCreated callback');
        onPostCreated();
      }
    },
    onError: (error) => {
      console.error('Failed to create post:', error);
      toast.error(error.message || 'Failed to create post');
    }
  });

  const handlePost = () => {
    if (isContentEmpty(content)) {
      toast.error('Please enter some content for your post');
      return;
    }

    const postData = {
      community: audience,
      audience: speciality,
      tags: tags.split(',').map(tag => tag.trim()).filter(Boolean),
      postStatus: 'published' as const,
      content: content,
    };

    console.log('Creating post with data:', postData);
    createPost(postData);
  };

  return (
    <CustomDialog
      open={open}
      onClose={onClose}
      title=""
      sx={{
        p: 0,
        px: { xs: 0, sm: '80px' },
        pt: { xs: 0, sm: '50px' },
        alignItems: { xs: 'stretch', sm: 'start' },
        '.MuiDialog-paper': {
          maxHeight: { xs: '100%', sm: 'calc(100% - 64px' },
        },
      }}
    >
      <Box
        display="flex"
        flexDirection="column"
        padding={{ xs: '16px', sm: '40px' }}
        width={'100%'}
        sx={{ backgroundColor: 'white' }}
      >
        {/* Heading */}
        <Box
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          height={'35px'}
        >
          <Box sx={{ display: 'flex', gap: '16px', alignItems: 'center' }}>
            {screenBelowSM && <BackButton onClick={onClose} />}
            <Typography
              sx={{
                fontFamily: 'Plus Jakarta Sans',
                fontWeight: 500,
                fontSize: { xs: '20px', sm: '28px' },
                color: '#1E1E1E',
              }}
            >
              New Text Post
            </Typography>
          </Box>
          <Typography
            sx={{
              fontFamily: 'Plus Jakarta Sans',
              fontWeight: 600,
              fontSize: '16px',
              color: '#A24295',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
            }}
          >
            Drafts <Iconify icon="solar:arrow-right-linear" />
          </Typography>
        </Box>

        {/* Rich Text Editor */}
        {/* <RichTextEditor
          value={
            content.length
              ? content
              : [{ type: 'paragraph', children: [{ text: '' }] }]
          }
          onChange={setContent}
        /> */}

        <CustomEditorBase
          value={content}
          onChange={setContent}
          label={'Your post'}
          placeholder="Write your content here!"
        />

        {/* Tags and Toggles */}
        <Box
          display="flex"
          flexDirection={{ xs: 'column', md: 'row', lg: 'row' }}
          gap={{ xs: '20px', lg: '40px' }}
          alignItems={{ xs: 'center', md: 'end' }}
          mt={{ xs: '40px', sm: '20px' }}
          mb={{ xs: '140px', sm: '0px' }}
        >
          <TextField
            placeholder="#Surgery #Valve"
            value={tags}
            onChange={(e) => setTags(e.target.value)}
            fullWidth
            label="Tags"
            InputLabelProps={{ shrink: true }}
          />
          <Box width={{ xs: '100%', md: '224px' }}>
            <CustomToggleButtonGroup
              label="Community"
              options={['PROFESSIONAL', 'PUBLIC', 'BOTH']}
              selected={audience}
              onChange={setAudience}
              width={{ xs: '100%', md: '224px' }}
            />
          </Box>
          <Box width={{ xs: '100%', md: '282px' }}>
            <CustomToggleButtonGroup
              label="Audience"
              options={['CARDIAC_SURGEON', 'CARDIOLOGIST', 'BOTH']}
              selected={speciality}
              onChange={setSpeciality}
              width={{ xs: '100%', md: '282px' }}
            />
          </Box>
        </Box>
        {/* Footer Buttons */}
        <Stack
          direction={{ xs: 'column', sm: 'row' }}
          spacing={{ xs: '10px', sm: '20px' }}
          justifyContent={{ xs: 'center', sm: 'center' }}
          sx={{
            position: { xs: 'fixed', sm: 'static' },
            bottom: 0,
            left: 0,
            mt: { xs: '0px', sm: '40px' },
            width: { xs: '100%', sm: 'auto' },
            background: { xs: 'rgba(255,255,255,0.8)', sm: 'transparent' },
            backdropFilter: { xs: 'blur(20px)', sm: 'none' },
            padding: { xs: '20px', sm: 0 },
            boxShadow: { xs: '0 -4px 20px 0 rgba(0,0,0,0.1)', sm: 'none' },
          }}
        >
          <Box
            sx={{
              display: 'flex',
              width: '100%',
              justifyContent: 'center',
              alignItems: 'center',
              flexDirection: { xs: 'column', sm: 'row' },
              gap: '20px',
            }}
          >
            <LoadingButton
              variant="outlined"
              onClick={onClose}
              sx={{
                width: '156px',
                height: '40px',
                backgroundColor: 'white',
                border: { xs: 'none', sm: '1px solid #A24295' },
                color: '#A24295',
                '&:hover': {
                  backgroundColor: { xs: '#f5f5f5', sm: 'secondary.light' },
                },
                fontSize: '16px',
                fontWeight: 700,
              }}
            >
              Cancel
            </LoadingButton>

            <PostButton
              setAnchorEl={setAnchorEl}
              handlePost={handlePost}
              disabled={isContentEmpty(content) || !audience || isPending}
              isOpen={openMenu}
            />
          </Box>
          <Menu
            anchorEl={anchorEl}
            open={openMenu}
            onClose={() => setAnchorEl(null)}
          >
            <MenuItem
              onClick={() => {
                setOpenScheduleDialog();
              }}
            >
              Schedule
            </MenuItem>
            <MenuItem
              onClick={() => {
                setAnchorEl(null);
              }}
            >
              Save Draft
            </MenuItem>
            <MenuItem
              onClick={() => {
                setAnchorEl(null);
              }}
            >
              Add to Sponsorship Queue
            </MenuItem>
          </Menu>
        </Stack>
      </Box>
    </CustomDialog>
  );
};

export default TextPostDialog;
