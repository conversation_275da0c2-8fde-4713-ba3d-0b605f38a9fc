'use client';

import { useState } from 'react';
import { Box, Typography, Divider } from '@mui/material';
import { Comment as CommentType } from '@minicardiac-client/types';
import ActionButton from './ActionButton';
import Dot from './Dot';
import ReplyItem from './ReplyItem';
import AddCommentInput from './AddCommentInput';
import { useTranslations } from 'next-intl';
import { Profile } from '../navigations/Profile';
import { getTimeAgo } from '@minicardiac-client/utilities';
import { useMutation } from '@tanstack/react-query';
import { postApi } from '@minicardiac-client/apis';
import { toast } from 'react-toastify';
import CommentHeader from './CommentHeader';

const CommentItem = ({
  comment,
  allowPin = false,
  postId,
  currentUserId,
}: {
  comment: CommentType;
  allowPin?: boolean;
  postId?: string;
  currentUserId?: string;
}) => {
  const [showReplyInput, setShowReplyInput] = useState(false);
  const [showReplies, setShowReplies] = useState(false);
  const [fetchedReplies] = useState<CommentType[]>([]);
  const [isRepliesLoading, setIsRepliesLoading] = useState(false);

  const t = useTranslations('comment');
  const postedAgo = getTimeAgo(comment.postedAgo);

  const { mutateAsync: pinComment } = useMutation({
    mutationFn: async (commentId: string) => {
      await postApi.pinComment(commentId);
    },
    onSuccess: () => toast.success('Comment pinned successfully'),
    onError: () => toast.error('Failed to pin the comment'),
  });

  const handleToggleReplies = async () => {
    const nextShow = !showReplies;
    setShowReplies(nextShow);

    if (nextShow && fetchedReplies.length === 0 && postId && comment.id) {
      try {
        setIsRepliesLoading(true);
        const response = await postApi.getComments(postId, {
          parentCommentId: comment.id,
          limit: 5,
          offset: 10,
        });

        console.log(response, 'response');

        // console.log(`Replies for comment ${comment.id}:`, response.comment);
      } catch (err) {
        console.error('Error fetching replies:', err);
        toast.error('Failed to load replies');
      } finally {
        setIsRepliesLoading(false);
      }
    }
  };

  const handlePin = () => {
    if (!comment.id) return;
    pinComment(comment.id);
  };

  return (
    <Box display="flex" gap="12px">
      <Profile
        displayName={comment.user.name}
        photoURL={comment.user.profilePic}
        size={40}
      />
      <Box flex={1} bgcolor="#F3F4F6" p="12px" borderRadius="8px">
        <CommentHeader name={comment.user.name} postedAgo={postedAgo} />

        <Typography mt="8px" fontSize="14px" fontWeight={400}>
          {comment.comment}
        </Typography>

        <Box mt="8px" display="flex" gap="16px" alignItems="center" ml="8px">
          <ActionButton label={t('like')} />
          <Dot />
          <ActionButton
            label={t('reply')}
            onClick={() => setShowReplyInput((prev) => !prev)}
          />
          {allowPin && comment.user.id === currentUserId && (
            <>
              <Dot />
              <ActionButton label={t('pinAnswer')} onClick={handlePin} />
            </>
          )}
        </Box>

        {showReplyInput && (
          <AddCommentInput postId={postId ?? ''} parentCommentId={comment.id} />
        )}

        {comment.repliesCount > 0 && (
          <>
            <Box
              mt="4px"
              ml="4px"
              sx={{ cursor: 'pointer' }}
              onClick={handleToggleReplies}
            >
              <Typography
                fontSize="12px"
                fontWeight={500}
                color="secondary.main"
              >
                {showReplies
                  ? `Hide ${comment.repliesCount} repl${
                      comment.repliesCount === 1 ? 'y' : 'ies'
                    }`
                  : `See ${comment.repliesCount} repl${
                      comment.repliesCount === 1 ? 'y' : 'ies'
                    }`}
              </Typography>
            </Box>

            {showReplies && (
              <>
                <Divider sx={{ my: 2, borderColor: '#A3A3A3', opacity: 0.5 }} />
                <Box mt="8px" display="flex" flexDirection="column" gap="8px">
                  {isRepliesLoading ? (
                    <Typography fontSize="12px" color="text.secondary">
                      Loading replies...
                    </Typography>
                  ) : (
                    fetchedReplies.map((reply) => (
                      <ReplyItem key={reply.id} reply={reply} />
                    ))
                  )}
                </Box>
              </>
            )}
          </>
        )}
      </Box>
    </Box>
  );
};

export default CommentItem;
