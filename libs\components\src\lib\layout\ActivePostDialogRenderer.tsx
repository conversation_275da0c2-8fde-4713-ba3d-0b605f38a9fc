'use client';

import { useState } from 'react';
import TextPostDialog from '../dialogs/TextPostDialog';
import MediaPostDialog from '../dialogs/MediaPostDialog';
import SchedulePostDialog from '../dialogs/ScheduleDialog';
import ArticlePostDialog from '../dialogs/article-post/ArticlePostDialog';
import PollPostDialog from '../dialogs/poll-post/PollPostDialog';
import QuestionPostDialog from '../dialogs/QuestionPostDialog';
import { usePostDialogStore } from '@minicardiac-client/apis';
import SavePostDialog from '../dialogs/SavePostDialog';
import AddNewFolderDialog from '../dialogs/AddNewFolderDialog';
import AllProfileDialog from '../dialogs/AllProfileDialog';

interface ActivePostDialogRendererProps {
  onPostCreated?: () => void;
}

export default function ActivePostDialogRenderer({
  onPostCreated,
}: ActivePostDialogRendererProps) {
  const { activeDialog, setActiveDialog } = usePostDialogStore();
  const [previousDialog, setPreviousDialog] = useState<string | null>(null);
  const [content, setContent] = useState('');
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);

  const closeDialog = () => setActiveDialog(null);

  return (
    <>
      {activeDialog === 'Text' && (
        <TextPostDialog
          open
          onClose={closeDialog}
          setOpenScheduleDialog={() => {
            setPreviousDialog('Text');
            setActiveDialog('Scheduled');
          }}
          content={content}
          setContent={setContent}
          onPostCreated={onPostCreated}
        />
      )}
      {activeDialog === 'Media' && (
        <MediaPostDialog
          open
          onClose={closeDialog}
          setOpenScheduleDialog={() => {
            setPreviousDialog('Media');
            setActiveDialog('Scheduled');
          }}
          uploadedFiles={uploadedFiles}
          setUploadedFiles={setUploadedFiles}
        />
      )}
      {activeDialog === 'Article' && (
        <ArticlePostDialog
          open
          onClose={closeDialog}
          setOpenScheduleDialog={() => {
            setPreviousDialog('Article');
            setActiveDialog('Scheduled');
          }}
          content={content}
          setContent={setContent}
        />
      )}
      {activeDialog === 'Poll' && (
        <PollPostDialog
          open
          onClose={closeDialog}
          setOpenScheduleDialog={() => {
            setPreviousDialog('Poll');
            setActiveDialog('Scheduled');
          }}
          content={content}
          setContent={setContent}
        />
      )}
      {activeDialog === 'Question' && (
        <QuestionPostDialog
          open
          onClose={closeDialog}
          setOpenScheduleDialog={() => {
            setPreviousDialog('Question');
            setActiveDialog('Scheduled');
          }}
          content={content}
          setContent={setContent}
        />
      )}
      {activeDialog === 'Scheduled' && (
        <SchedulePostDialog
          open
          onClose={closeDialog}
          returnToDialog={previousDialog}
          setReturnDialogOpen={(val) => {
            if (val && previousDialog) setActiveDialog(previousDialog);
            else setActiveDialog(null);
          }}
        />
      )}
      {activeDialog === 'SavePost' && (
        <SavePostDialog open onClose={closeDialog} />
      )}
      {activeDialog === 'NewFolder' && (
        <AddNewFolderDialog open onClose={closeDialog} />
      )}
      {activeDialog === 'AllProfile' && (
        <AllProfileDialog open onClose={closeDialog} />
      )}
    </>
  );
}
