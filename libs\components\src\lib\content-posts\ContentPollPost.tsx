'use client';

import { forwardRef, useState } from 'react';
import { Box } from '@mui/material';
import { POLL_USER, POLL_CAPTION, POLL_STATS } from '../poll-post/constants';
import PostFooterActions from '../content-posts/PostFooterActions';
import PostHeader from '../content-posts/PostHeader';
import { PollCaption } from '../poll-post/PollCaption';
import MainPollContent from '../poll-post/MainPollContent';

const ContentPollPost = forwardRef(function ContentPollPost(
  {
    postId,
    ghost = false,
    ...eventHandlers
  }: {
    postId?: string;
    ghost?: boolean;
  } & React.HTMLAttributes<HTMLDivElement>,
  ref: React.Ref<HTMLDivElement>
) {
  const [showComments, setShowComments] = useState(false);

  const handleCommentClick = () => {
    setShowComments((prev) => !prev);
  };

  return (
    <Box
      ref={ref}
      sx={{
        width: '100%',
        p: { xs: '16px', sm: '20px' },
        borderRadius: '8px',
        backgroundColor: '#fff',
        boxShadow: '0px 1px 6px rgba(0, 0, 0, 0.05)',
        boxSizing: 'border-box',
        display: 'flex',
        flexDirection: 'column',
        gap: '20px',
        cursor: eventHandlers.onMouseDown ? 'grab' : 'default',
      }}
      {...eventHandlers}
    >
      <PostHeader user={POLL_USER} showOptions />
      <PollCaption POLL_CAPTION={POLL_CAPTION} />
      <MainPollContent />
      <PostFooterActions
        likes={POLL_STATS.likes}
        isLiked={false}
        commentsCount={POLL_STATS.comments}
        reposts={POLL_STATS.reposts}
        shares={POLL_STATS.shares}
        onOpenComments={handleCommentClick}
        showComments={showComments}
        setShowComments={setShowComments}
      />
    </Box>
  );
});

export default ContentPollPost;
