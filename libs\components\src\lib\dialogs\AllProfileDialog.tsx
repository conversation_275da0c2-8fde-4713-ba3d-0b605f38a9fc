'use client';

import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  useMediaQuery,
} from '@mui/material';
import { useTranslations } from 'next-intl';
import { useMemo, useState } from 'react';
import { Profile } from '../navigations/Profile';
import { SearchBar } from '../search-bar/SearchBar';
import { useTheme } from '@mui/material/styles';
import { BackButton } from '../buttons/Backbutton';
import { usePostDialogStore } from '@minicardiac-client/apis';

interface AllProfileDialogProps {
  open: boolean;
  onClose: () => void;
}

const dummyProfiles = Array.from({ length: 25 }).map((_, i) => ({
  id: `${i + 1}`,
  name: `Hospital ${i + 1}`,
  image: '/images/hospital1.png',
}));

export default function AllProfileDialog({
  open,
  onClose,
}: AllProfileDialogProps) {
  const t = useTranslations('allProfilesDialog');
  const [searchQuery] = useState('');
  const [selectedProfileId, setSelectedProfileId] = useState<string | null>(
    null
  );
  const { setActiveDialog } = usePostDialogStore();

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const profiles = dummyProfiles;

  const filteredProfiles = useMemo(() => {
    if (!searchQuery.trim()) return profiles;
    return profiles.filter((profile) =>
      profile.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [searchQuery, profiles]);

  const handleSwitch = () => {
    console.log('Switched to profile:', selectedProfileId);
    onClose();
  };

  const showSearch = profiles.length > 20;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullScreen={isMobile}
      PaperProps={{
        sx: (theme) => ({
          width: '100vw',
          maxWidth: isMobile ? '100vw' : '807px',
          backgroundColor: '#fff',
          display: 'flex',
          flexDirection: 'column',
          borderRadius: {
            xs: 0,
            sm: '12px',
          },
          height: {
            xs: '100vh',
            sm: '485px',
          },
        }),
      }}
    >
      <DialogContent
        sx={{
          padding: 0,
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
        }}
      >
        {/* Top Section */}
        <Box
          sx={{
            paddingX: isMobile ? '16px' : '40px',
            paddingTop: isMobile ? '24px' : '40px',
            flexGrow: 1,
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <Box
            display="flex"
            justifyContent={
              !isMobile && showSearch ? 'space-between' : 'flex-start'
            }
            flexDirection={isMobile ? 'column' : 'row'}
            mb={isMobile ? '24px' : '40px'}
            gap={isMobile ? '16px' : '40px'}
            width="100%"
          >
            <Box
              sx={{ display: 'flex', alignItems: 'flex-start', gap: '16px' }}
            >
              {isMobile && <BackButton onClick={() => setActiveDialog(null)} />}
              <Typography
                fontSize={isMobile ? '20px' : '28px'}
                fontWeight={500}
                color="#1E1E1E"
                fontFamily="Plus Jakarta Sans"
                whiteSpace="nowrap"
              >
                {t('title')}
              </Typography>
            </Box>
            {showSearch && <SearchBar />}
          </Box>

          <Box
            sx={{
              flexGrow: 1,
              overflowY: 'auto',
              pr: 1,
              scrollbarWidth: 'none',
              '&::-webkit-scrollbar': { display: 'none' },
              mb: '10px',
            }}
          >
            <Box
              sx={{
                display: 'grid',
                gridTemplateColumns: isMobile
                  ? 'repeat(1, 1fr)'
                  : 'repeat(3, 1fr)',
                gap: '20px',
              }}
            >
              {filteredProfiles.map((profile) => {
                const isSelected = selectedProfileId === profile.id;
                return (
                  <Box
                    key={profile.id}
                    onClick={() => setSelectedProfileId(profile.id)}
                    sx={{
                      cursor: 'pointer',
                      border: isSelected
                        ? '3px solid #A24295'
                        : '1px solid #A24295',
                      borderRadius: '8px',
                      padding: '16px',
                      display: 'flex',
                      alignItems: 'center',
                      width: '100%',
                      height: '64px',
                      transition: 'border 0.2s',
                      gap: '8px',
                    }}
                  >
                    <Profile
                      displayName={profile.name}
                      photoURL={profile.image}
                      size={32}
                      sx={{ mx: '0px' }}
                    />
                    <Typography
                      fontSize="16px"
                      fontWeight={isSelected ? 600 : 400}
                      color={isSelected ? '#A24295' : '#1E1E1E'}
                      fontFamily="Plus Jakarta Sans"
                      sx={{
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }}
                    >
                      {profile.name}
                    </Typography>
                  </Box>
                );
              })}
            </Box>
          </Box>
        </Box>

        {/* Bottom Sticky Section */}
        <Box
          sx={{
            position: 'sticky',
            bottom: 0,
            backgroundColor: '#fff',
            boxShadow: '0px -4px 20px rgba(0, 0, 0, 0.04)',
            padding: '24px',
            display: 'flex',
            justifyContent: 'center',
            gap: '16px',
            zIndex: 1,
          }}
        >
          <Button
            variant="outlined"
            onClick={onClose}
            sx={{
              width: '100%',
              display: isMobile ? 'none' : 'block',
              maxWidth: '156px',
              height: '40px',
              fontWeight: 700,
              borderColor: '#A24295',
              color: '#A24295',
              '&:hover': {
                backgroundColor: '#f9f0f5',
              },
            }}
          >
            {t('cancel')}
          </Button>
          <Button
            variant="contained"
            onClick={handleSwitch}
            disabled={!selectedProfileId}
            sx={{
              width: '100%',
              maxWidth: isMobile ? '100%' : '156px',
              height: '40px',
              fontWeight: 700,
              backgroundColor: selectedProfileId ? '#A24295' : '#A3A3A3',
              color: '#fff',
              '&:hover': {
                backgroundColor: selectedProfileId ? '#932080' : '#A3A3A3',
              },
              '&.Mui-disabled': {
                backgroundColor: '#A3A3A3',
                color: '#fff',
              },
            }}
          >
            {t('switch')}
          </Button>
        </Box>
      </DialogContent>
    </Dialog>
  );
}
