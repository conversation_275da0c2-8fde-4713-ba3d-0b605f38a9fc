import { useState } from 'react';

const HomeIcon = ({ fill = '#A3A3A3', hoverFill = '#A24295', size = 24 }) => {
  const [isHovered, setIsHovered] = useState(false);
  const currentFill = isHovered ? hoverFill : fill;

  return (
    <svg
      width="24"
      height="22"
      viewBox="0 0 24 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.9838 0H12.0162C12.0812 0 12.1786 0.0324763 12.2436 0.097429L23.9026 10.3599C24.0325 10.4899 24.0325 10.6847 23.9026 10.8146L23.4154 11.2043C23.2855 11.3342 23.0582 11.3342 22.9283 11.2043L20.4276 8.99594V19.3884C20.4276 20.3302 19.6807 21.1096 18.7713 21.1096H15.2314V13.1204C15.2314 12.7632 14.9391 12.4709 14.5819 12.4709H9.38566C9.06089 12.4709 8.76861 12.7632 8.76861 13.1204V21.1096H5.19621C4.28687 21.1096 3.53992 20.3302 3.53992 19.3884V8.99594L1.03924 11.2043C0.909337 11.3342 0.682003 11.3342 0.552097 11.2043L0.097429 10.8146C-0.0324763 10.6847 -0.0324763 10.4899 0.097429 10.3599L11.7564 0.097429C11.8214 0.0324763 11.8863 0 11.9838 0Z"
        fill={currentFill}
      />
    </svg>
  );
};

export default HomeIcon;
