# Page snapshot

```yaml
- button "Open Next.js Dev Tools":
  - img
- button "Open issues overlay": 1 Issue
- button "Collapse issues badge":
  - img
- alert
- img "Mini Card Icon"
- img "Background pattern"
- paragraph
- paragraph: Sign In / Sign Up
- img
- img
- img
- button "Home":
  - link "Home":
    - /url: /feed
    - img
    - paragraph: Home
- button "Network":
  - link "Network":
    - /url: "#"
    - img
    - paragraph: Network
- button "Opportunities":
  - link "Opportunities":
    - /url: "#"
    - img
    - paragraph: Opportunities
- button "My Workspace":
  - link "My Workspace":
    - /url: "#"
    - img
    - paragraph: My Workspace
- text: About MiniCardiac Contact Us | Legal | Cookie Policy
- img "Search"
- textbox "Search by keyword, poster, or tag": nonsensekeyword123
- button "Voice Search":
  - img
- img
- img "Profile Picture"
- paragraph: <PERSON>
- paragraph: Invalid date
- button
- button
- paragraph: With the growing body of evidence supporting the use of SGLT2 inhibitors in HFpEF patients, are you now routinely prescribing them as part of your initial management plan?
- paragraph: See more
- paragraph: This is the Poll Title
- paragraph: Option 1
- paragraph
- paragraph: Option 2
- paragraph
- paragraph: Select from Custom Answers
- button
- separator
- textbox "Do you have another suggestion? Type it here!"
- paragraph: Submit
- paragraph: Like (600)
- img
- paragraph: Comment (25)
- paragraph: Repost (12)
- paragraph: Share (5)
- paragraph: Tags
- paragraph: Suggested
- paragraph: "#CuttingEdge"
- paragraph: "#Clinics"
- paragraph: Followed
- paragraph: "#Surgery"
- paragraph: "#Technology"
- paragraph: "#Medicine"
- paragraph: "#Cardiology"
- paragraph: "#Surgery2"
- paragraph: "#Technology2"
- button "Browse all tags →"
- button "close"
- text: Join the community! Are you a part of the cardiac world? Join the community today to establish your professional presence, build your network, find jobs and opportunities, and collaborate with colleagues!
- button "Sign Up!"
- status
- region "Notifications Alt+T"
- button "Open Tanstack query devtools":
  - img
```