'use client';
import { Box, IconButton, InputBase } from '@mui/material';
import VoiceIcon from '../Icons/VoiceIcon';
import { SEARCH_ICON } from '../auth';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { useFeedSearchStore } from '../store/useFeedSearchStore';
import { useEffect, useState } from 'react';
import { useDebouncedValue } from '../hooks/useDebouncedValue';

export const SearchBar = () => {
  const t = useTranslations('feedNavigation');
  const searchKeyword = useFeedSearchStore((state) => state.searchKeyword);
  const setSearchKeyword = useFeedSearchStore(
    (state) => state.setSearchKeyword
  );

  // Local state for input value
  const [inputValue, setInputValue] = useState(searchKeyword);
  const debouncedInput = useDebouncedValue(inputValue, 300);

  // Removed debug console.log statements for production cleanliness

  // Keep local state in sync if searchKeyword changes externally
  useEffect(() => {
    setInputValue(searchKeyword);
  }, [searchKeyword]);

  // Only trigger search when debouncedInput has 4+ characters or is empty (to clear search)
  useEffect(() => {
    const trimmedInput = debouncedInput.trim();

    if (trimmedInput.length >= 4 || trimmedInput.length === 0) {
      setSearchKeyword(trimmedInput);
    }
  }, [debouncedInput, setSearchKeyword]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const value = (e.target as HTMLInputElement).value.trim();

      if (value.length >= 4 || value.length === 0) {
        setSearchKeyword(value);
      }
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        width: '100%',
        borderRadius: '8px',
        overflow: 'hidden',
        border: '1px solid #A3A3A3',
        backgroundColor: 'white',
      }}
    >
      <Box
        sx={{
          backgroundColor: 'white',
          padding: '10px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '50px',
          height: '47px',
        }}
      >
        <Image src={SEARCH_ICON} alt={t('search')} width={24} height={24} />
      </Box>
      <InputBase
        placeholder={t('searchPlaceholder')}
        sx={{
          flex: 1,
          backgroundColor: 'white',
        }}
        value={inputValue}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
      />
      <IconButton aria-label={t('voiceSearch')}>
        <VoiceIcon fill={'#A24295'} />
      </IconButton>
    </Box>
  );
};
