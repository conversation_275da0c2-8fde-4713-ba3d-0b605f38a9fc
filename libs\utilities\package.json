{"name": "@minicardiac-client/utilities", "version": "0.0.1", "private": true, "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"development": "./src/index.ts", "types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.js"}}, "dependencies": {"axios": "^1.6.0", "jwt-decode": "^4.0.0", "@minicardiac-client/apis": "workspace:*", "@mui/material": "^7.1.0", "react": "^18.2.0", "date-fns": "4.1.0"}}