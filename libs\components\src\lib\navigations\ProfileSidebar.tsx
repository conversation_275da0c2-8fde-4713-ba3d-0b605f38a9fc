'use client';

import { Box, Typography, IconButton } from '@mui/material';
import SettingsOutlinedIcon from '@mui/icons-material/SettingsOutlined';
import { useEffect, useRef } from 'react';
import { usePostDialogStore } from '@minicardiac-client/apis';
import { Profile } from './Profile';

type ProfileSidebarProps = {
  open: boolean;
  onClose: () => void;
};

export const otherProfiles: any = [
  {
    id: 1,
    name: '<PERSON>',
    photoURL: '/avatars/alex.jpg',
  },
  {
    id: 2,
    name: '<PERSON>',
    photoURL: '/avatars/maria.jpg',
  },
  {
    id: 3,
    name: '<PERSON>',
    photoURL: '/avatars/john.jpg',
  },
  {
    id: 4,
    name: '<PERSON>',
    photoURL: '/avatars/linda.jpg',
  },
  {
    id: 5,
    name: '<PERSON>',
    photoURL: '/avatars/daniel.jpg',
  },
  {
    id: 6,
    name: '<PERSON>',
    photoURL: '/avatars/emily.jpg',
  },
  {
    id: 7,
    name: '<PERSON>',
    photoURL: '/avatars/sophie.jpg',
  },
];

export const ProfileSidebar = ({ open, onClose }: ProfileSidebarProps) => {
  const { setActiveDialog } = usePostDialogStore();
  const sidebarRef = useRef<HTMLDivElement | null>(null);

  // Detect outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        sidebarRef.current &&
        !sidebarRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    if (open) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [open, onClose]);

  useEffect(() => {
    document.body.style.overflow = open ? 'hidden' : 'auto';
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [open]);

  if (!open) return null;

  const MAX_VISIBLE_PROFILES = 6;
  const visibleProfiles = otherProfiles.slice(0, MAX_VISIBLE_PROFILES);

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        width: '100vw',
        height: '100vh',
        zIndex: 1300,
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
      }}
    >
      <Box
        ref={sidebarRef}
        sx={{
          position: 'absolute',
          right: 0,
          top: 0,
          width: 'calc(100% - 64px)',
          height: '100%',
          backgroundColor: '#fff',
          borderTopLeftRadius: '20px',
          borderBottomLeftRadius: '20px',
          display: 'flex',
          flexDirection: 'column',
          p: '20px',
        }}
      >
        {/* Header */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 3,
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Profile
              size={40}
              // photoURL={profile?.photoURL}
              displayName={'Stephie Crawford'}
              sx={{ mx: '0px' }}
            />

            <Typography fontSize={20} fontWeight={600}>
              Stephie Crawford
            </Typography>
          </Box>
          <IconButton>
            <SettingsOutlinedIcon sx={{ color: '#A24295' }} />
          </IconButton>
        </Box>

        {/* Profiles */}
        <Box sx={{ mb: 3 }}>
          <Typography
            variant="body2"
            sx={{
              fontWeight: 600,
              fontSize: '12px',
              color: '#737678',
              px: '16px',
              mb: 1,
            }}
          >
            Profiles
          </Typography>

          <Box sx={{ position: 'relative', display: 'flex', width: '100%' }}>
            {/* Vertical divider */}
            <Box
              sx={{
                position: 'absolute',
                left: '32px',
                top: 0,
                bottom: 0,
                width: '1px',
                backgroundColor: '#A3A3A3',
                borderRadius: '1.5px',
              }}
            />

            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: '8px',
                width: '100%',
              }}
            >
              {visibleProfiles.map(
                (profile: { id: number; name: string; photoURL: string }) => (
                  <Box
                    key={profile.id}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      py: '8px',
                      cursor: 'pointer',
                      ':hover': { backgroundColor: '#F6ECF4' },
                      height: '40px',
                    }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        ml: '52px',
                        gap: '8px',
                      }}
                    >
                      <Profile
                        size={28}
                        photoURL={profile?.photoURL}
                        displayName={profile?.name}
                        sx={{ mx: '0px' }}
                      />
                      <Typography sx={{ fontSize: '16px', fontWeight: 400 }}>
                        {profile.name}
                      </Typography>
                    </Box>
                  </Box>
                )
              )}

              {/* All profiles link */}
              {otherProfiles.length > MAX_VISIBLE_PROFILES && (
                <Box
                  sx={{
                    backgroundColor: '#F6ECF4',
                    px: '16px',
                    py: '8px',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    height: '40px',
                    position: 'relative',
                    '&:hover': {
                      backgroundColor: '#f3e0ee',
                    },
                  }}
                  onClick={() => setActiveDialog('AllProfile')}
                >
                  <Box
                    sx={{
                      position: 'absolute',
                      left: '31px',
                      width: '3px',
                      backgroundColor: '#A24295',
                      height: '40px',
                    }}
                  />
                  <Typography
                    sx={{ fontSize: '16px', fontWeight: 400, ml: '40px' }}
                  >
                    All profiles →
                  </Typography>
                </Box>
              )}
            </Box>
          </Box>
        </Box>

        {/* Activity Section Header */}
        <Box>
          <Typography
            variant="body2"
            sx={{
              fontWeight: 400,
              fontSize: '16px',
              px: '16px',
              mb: 2,
            }}
          >
            Activity
          </Typography>

          {/* Add activity items here */}
        </Box>

        {/* Logout Button */}
        <Box
          sx={{
            mt: 'auto', // pushes it to bottom inside flex column
            height: '50px',
            boxShadow: '0px -4px 20px rgba(0, 0, 0, 0.04)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            cursor: 'pointer',
            pt: '20px',
          }}
        >
          <Typography
            sx={{
              fontSize: '16px',
              fontWeight: 600,
              color: '#A24295',
            }}
          >
            Logout
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};
