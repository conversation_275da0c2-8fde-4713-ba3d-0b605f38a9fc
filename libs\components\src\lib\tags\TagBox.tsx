'use client';

import {
  Box,
  Button,
  SxProps,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import PostCard from '../content-posts/PostCard';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

interface FollowButtonProps {
  isFollowing: boolean;
  onClick: () => void;
}

interface TagBoxProps {
  tag: string;
  sx?: SxProps;
  followButtonProps?: FollowButtonProps;
}

export default function TagBox({ tag, sx, followButtonProps }: TagBoxProps) {
  const theme = useTheme();
  const router = useRouter();
  const isSmallOrMedium = useMediaQuery(theme.breakpoints.down('lg'));

  const [isFollowing, setIsFollowing] = useState(
    followButtonProps?.isFollowing ?? false
  );

  const handleFollowClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent box click
    if (followButtonProps) {
      setIsFollowing((prev) => !prev);
      followButtonProps.onClick();
    }
  };

  const handleNavigate = () => {
    router.push(`/feed?hashtag=${encodeURIComponent(tag)}`);
  };

  return (
    <Box
      onClick={handleNavigate}
      sx={{
        backgroundColor: '#F8F9FA',
        borderRadius: '8px',
        padding: '16px',
        transition: 'background-color 0.2s ease',
        '&:hover': {
          backgroundColor: '#F6ECF4',
        },
        width: '100%',
        cursor: 'pointer',
        ...sx,
      }}
    >
      {/* Tag name and follow button row */}
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={2}
      >
        <Typography fontWeight={700} fontSize="16px" color="#A24295">
          #{tag}
        </Typography>

        {followButtonProps && (
          <Button
            onClick={handleFollowClick}
            variant="outlined"
            sx={{
              height: '28px',
              borderRadius: '8px',
              borderColor: '#A24295',
              color: '#A24295',
              textTransform: 'none',
              fontWeight: 700,
              fontSize: '12px',
              minWidth: 'auto',
              padding: '0 12px',
              lineHeight: 1.5,
            }}
          >
            {isFollowing ? 'Following' : 'Follow'}
          </Button>
        )}
      </Box>

      {/* Post previews */}
      <Box
        sx={{
          display: 'flex',
          gap: '16px',
          justifyContent: { xs: 'space-around', md: 'space-between' },
          overflowX: isSmallOrMedium ? 'auto' : 'visible',
          paddingBottom: '4px',
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
          '&::-webkit-scrollbar': {
            display: 'none',
          },
        }}
      >
        {[...Array(2)].map((_, i) => (
          <PostCard key={i} />
        ))}
      </Box>
    </Box>
  );
}
