'use client';

import { Box, Stack, Typography } from '@mui/material';
import { SIGNUP_OPTIONS } from '../constants/auth.constants';
import SignupOptionCard from './SignupOptionCard';
import { ExtendedTheme } from '../types/auth.types';
import { LoadingButton } from '../../loading-button';
import { useAuthStore } from '@minicardiac-client/apis';
import { useState, useEffect } from 'react';

interface SignupTypeListProps {
  onTypeSelect: (path: string) => void;
}

/**
 * SignupTypeList component for displaying signup options
 */
export const SignupTypeList = ({ onTypeSelect }: SignupTypeListProps) => {
  // Get auth UI state from Zustand store
  const { isSigningUp, setSigningUp, setSelectedUserType } = useAuthStore();
  const [selectedOption, setSelectedOption] = useState<string | null>(null);

  useEffect(() => {
    // Reset signingUp if we're no longer on the exact /signup page
    if (window.location.pathname !== '/signup') {
      setSigningUp(false);
    }
  }, [setSigningUp]);

  const handleCardSelect = (path: string) => {
    const userType = path.split('/').pop() || null;
    setSelectedOption(userType);
  };

  const handleContinue = () => {
    if (!selectedOption || isSigningUp) return;

    setSigningUp(true);
    setSelectedUserType(selectedOption);

    // Call the original onTypeSelect handler with the full path
    onTypeSelect(`/signup/${selectedOption}`);
  };

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
      }}
    >
      <Box
        sx={{
          mb: { xs: '20px', sm: '16px' },
          mt: { sm: '20px' },
          display: 'flex',
          justifyContent: 'center',
        }}
      >
        <Typography
          variant="body1"
          sx={(theme) => ({
            fontFamily: theme.typography.fontFamily,
            fontWeight: 400,
            fontSize: '16px',
            lineHeight: '24px',
            letterSpacing: '0px',
            textAlign: 'center',
            verticalAlign: 'middle',
            color: theme.palette.text.secondary,
          })}
        >
          Sign up as
        </Typography>
      </Box>

      <Stack
        sx={{
          width: '100%',
          maxWidth: '740px',
          alignItems: 'center',
          position: 'relative',
        }}
        gap={'1.2rem'}
      >
        {SIGNUP_OPTIONS.map((option) => (
          <SignupOptionCard
            key={option.title}
            option={option}
            onClick={handleCardSelect}
            selected={selectedOption === option.path.split('/').pop()}
          />
        ))}
      </Stack>

      <Box
        sx={{
          position: {
            xs: 'fixed',
            sm: 'relative',
          },
          bottom: {
            xs: 0,
            sm: 'auto',
          },
          left: {
            xs: 0,
            sm: 'auto',
          },
          width: {
            xs: '100%',
            sm: 'auto',
          },
          px: {
            xs: 2,
            sm: 0,
          },
          py: {
            xs: '20px',
            sm: 0,
          },
          backgroundColor: {
            xs: '#fff',
            sm: 'transparent',
          },
          boxShadow: {
            xs: '0 -4px 20px rgba(0,0,0,0.05)',
            sm: 'none',
          },
          zIndex: 100,

          // ✅ CENTER the button
          display: 'flex',
          justifyContent: 'center',
        }}
      >
        <LoadingButton
          disabled={!selectedOption || isSigningUp}
          loading={isSigningUp}
          loadingText="Signing up..."
          onClick={handleContinue}
          sx={(theme: any) => ({
            width: {
              xs: '280px',
              sm: '360px',
              md: '360px',
              // lg: '400px',
              // xxl: '480px',
            },
            height: (theme as ExtendedTheme).customValues.button?.height,
            gap: (theme as ExtendedTheme).customValues.button?.spacing,
            backgroundColor:
              selectedOption && (theme.palette as any).secondary.main,
            '&:hover': {
              backgroundColor:
                selectedOption && (theme.palette as any).secondary.dark,
            },
            mt: { xs: 0, sm: '16px' },
            borderRadius: {
              xs: '8px',
              sm: '8px',
            },
            fontSize: '16px',
            fontWeight: 500,
          })}
        >
          {isSigningUp ? 'Signing up...' : 'Continue'}
        </LoadingButton>
      </Box>
    </Box>
  );
};

export default SignupTypeList;
