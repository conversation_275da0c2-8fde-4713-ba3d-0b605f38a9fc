'use client';
import { Box, Stack, Typography } from '@mui/material';
import TagBox from './TagBox';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';

interface TagSectionProps {
  title: string;
  tags: string[];
  tagType: 'suggested' | 'your-tags';
  showAll?: boolean;
  hideSeeAll?: boolean;
}

export default function TagSection({
  title,
  tags,
  tagType,
  showAll = false,
  hideSeeAll = false,
}: TagSectionProps) {
  const router = useRouter();
  const t = useTranslations('tagsPage');

  const handleSeeAllClick = () => {
    router.push(`/feed/tags?tagType=${tagType}`);
  };

  const isSuggested = tagType === 'suggested';

  return (
    <Box
      mb={4}
      sx={{
        backgroundColor: { xs: '', sm: '#FFFFFF' },
        padding: { xs: '0px', sm: '20px' },
        borderRadius: '8px',
      }}
    >
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={2}
      >
        <Typography
          fontSize={{ xs: '20px', sm: '16px' }}
          fontWeight={{ xs: 500, sm: 700 }}
        >
          {title}
        </Typography>

        {!hideSeeAll && (
          <Typography
            fontSize="16px"
            fontWeight={600}
            color="#A24295"
            sx={{ cursor: 'pointer' }}
            onClick={handleSeeAllClick}
          >
            {t('seeAll')} →
          </Typography>
        )}
      </Box>

      <Stack
        sx={{
          display: 'flex',
          gap: '16px',
          flexDirection: { xs: 'column', sm: 'row' },
          overflowX: showAll ? 'unset' : 'scroll',
          flexWrap: showAll ? 'wrap' : 'nowrap',
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
          '&::-webkit-scrollbar': { display: 'none' },
        }}
      >
        {tags.map((tag) => (
          <TagBox
            key={tag}
            tag={tag}
            {...(isSuggested && {
              followButtonProps: {
                isFollowing: false,
                onClick: () => console.log(`Followed ${tag}`),
              },
            })}
          />
        ))}
      </Stack>
    </Box>
  );
}
