'use client';

import { <PERSON>, Stack, Typography, Alert } from '@mui/material';
import PasswordField from './PasswordField';
import OrDivider from './OrDivider';
import SocialLoginButton from './SocialLoginButton';
import { LoadingButton } from '../../loading-button';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { TextField } from '../../components/common/TextField';
import LocaleSwitcher from '../../intl-i18n/LocaleSwitcher';

interface SignInFormProps {
  onSubmit?: (data: { email: string; password: string }) => void;
  onForgotPassword?: () => void;
  onGoogleSignIn?: () => void;
  onAppleSignIn?: () => void;
  error?: string | null;
  isLoading?: boolean;
  emailLabel?: string;
  passwordLabel?: string;
  forgotPasswordLabel?: string;
  continueLabel?: string;
  orLabel?: string;
  googleLabel?: string;
  appleLabel?: string;
  emailPlaceholder?: string;
  passwordPlaceholder?: string;
  emailErrorMessage?: string;
  passwordErrorMessage?: string;
  forgotPasswordLinkText?: string;
  signInButtonText?: string;
  googleSignInButtonText?: string;
  appleSignInButtonText?: string;
  showPassword?: boolean;
  onTogglePasswordVisibility?: () => void;
}

const schema = yup.object({
  email: yup
    .string()
    .email('Enter a valid email')
    .required('Email is required'),
  password: yup.string().required('Password is required'),
});

/**
 * SignInForm component for the sign in page
 */
export const SignInForm = ({
  onSubmit = () => {
    // Intentional no-op: Default implementation
  },
  onForgotPassword = () => {
    // Intentional no-op: Default implementation
  },
  onGoogleSignIn = () => {
    // Intentional no-op: Default implementation
  },
  onAppleSignIn = () => {
    // Intentional no-op: Default implementation
  },
  error = null,
  isLoading = false,
  emailLabel = 'Email',
  passwordLabel = 'Password',
  forgotPasswordLabel = 'Forgot Password?',
  continueLabel = 'Continue',
  orLabel = 'OR',
  googleLabel = 'Continue with Google',
  appleLabel = 'Continue with Apple',
  emailPlaceholder = '<EMAIL>',
  passwordPlaceholder = '*******',
  emailErrorMessage = 'Please enter a valid email',
  passwordErrorMessage = 'Password is required',
  forgotPasswordLinkText = 'Forgot Password?',
  signInButtonText = 'Sign In',
  googleSignInButtonText = 'Sign in with Google',
  appleSignInButtonText = 'Sign in with Apple',
  showPassword = false,
  onTogglePasswordVisibility = () => {
    /* no-op */
  },
}: SignInFormProps) => {
  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
  } = useForm<{ email: string; password: string }>({
    resolver: yupResolver(schema),
    mode: 'onChange',
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const onFormSubmit = (data: { email: string; password: string }) => {
    onSubmit(data);
  };

  const handleGoogleSignIn = () => {
    onGoogleSignIn();
  };

  const handleAppleSignIn = () => {
    onAppleSignIn();
  };

  return (
    <Stack
      sx={{
        pb: { xs: '36px', sm: '40px' },
        py: '1.2rem',
        width: {
          xs: '280px',
          sm: '320px',
          md: '320px',
          lg: '1/2',
        },
        fontSize: {
          xxl: '1.2rem',
        },
      }}
    >

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          gap: '24px',
        }}
      >
        <TextField
          fullWidth
          label={emailLabel}
          placeholder={emailPlaceholder}
          variant="outlined"
          size="small"
          {...register('email')}
          error={!!errors.email}
          helperText={errors.email?.message as string}
          disabled={isLoading}
          InputLabelProps={{
            shrink: true,
            sx: {
              fontFamily: 'Plus Jakarta Sans, sans-serif',
            },
          }}
          inputProps={{
            style: {
              height: '45px', // Matches Figma height
              boxSizing: 'border-box',
            },
          }}
          sx={{
            '& .MuiInputLabel-root': {
              fontSize: '22px',
              fontWeight: 600,
              fontFamily: 'Plus Jakarta Sans, sans-serif',
            },
            '& .MuiInputBase-input': {
              fontFamily: 'Plus Jakarta Sans, sans-serif',
              fontSize: {
                xs: '14px',
                xxl: '18px',
              },
              padding: '12px 14px',
            },
            '& .MuiFormHelperText-root': {
              fontFamily: 'Plus Jakarta Sans, sans-serif',
            },
            '& .MuiInputLabel-root.Mui-error': {
              color: '#FF5C5C',
            },
            '& .MuiOutlinedInput-root': {
              borderRadius: '8px',
              '& fieldset': {
                border: '1.5px solid #9CA3AF !important', // Increased width and slightly darker color
              },
              '&:hover fieldset': {
                border: '1.5px solid #9CA3AF !important',
              },
              '&.Mui-focused fieldset': {
                border: '1.5px solid #9CA3AF !important',
              },
            },
          }}
        />

        <PasswordField
          {...register('password')}
          label={passwordLabel}
          placeholder={passwordPlaceholder}
          error={!!errors.password}
          helperText={errors.password?.message as string}
          disabled={isLoading}
          showPassword={showPassword}
          onToggleVisibility={onTogglePasswordVisibility}
        />
      </Box>
      
      {/* Error message */}
      {error && (
        <Alert 
          severity="error" 
          sx={{ 
            mt: 2,
            '& .MuiAlert-message': {
              fontSize: '14px',
              fontFamily: 'Plus Jakarta Sans, sans-serif',
            }
          }}
        >
          {error}
        </Alert>
      )}
      {/* Forgot Password Link */}
      <Box
        sx={{
          width: '100%',
          display: 'flex',
          justifyContent: 'center',
          mt: { xs: '32px', sm: '26px' },
        }}
      >
        <Typography
          variant="body2"
          color="secondary.main"
          onClick={() => onForgotPassword()}
          sx={{
            cursor: 'pointer',
            fontFamily: 'Plus Jakarta Sans',
            fontWeight: 600,
            fontSize: '16px',
            lineHeight: (theme: any) =>
              theme.customValues.typography?.button?.medium?.lineHeight ||
              '24px',
            textDecoration: 'none',
            '&:hover': {
              textDecoration: 'underline',
            },
            opacity: isLoading ? 0.7 : 1,
            pointerEvents: isLoading ? 'none' : 'auto',
          }}
        >
          {forgotPasswordLabel}
        </Typography>
      </Box>
      <LoadingButton
        onClick={handleSubmit(onFormSubmit)}
        loading={isLoading}
        loadingText="Signing in..."
        disabled={isLoading || !isValid}
        fullWidth
        variant="contained"
        type="submit"
        sx={{
          mt: 2, 
          fontSize: '1rem',
          fontWeight: 600,
          textTransform: 'none',
          borderRadius: '8px',
          backgroundColor: 'secondary.main',
          '&:hover': {
            backgroundColor: 'secondary.dark',
          },
          '&.Mui-disabled': {
            backgroundColor: 'action.disabled',
            color: 'text.disabled',
          },
        }}
      >
        {continueLabel}
      </LoadingButton>
      <OrDivider text={orLabel} />
      {/* Social Login Buttons */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: { xs: 'row', sm: 'column' },
          justifyContent: 'center',
          gap: { xs: '40px', sm: '16px' },
        }}
      >
        <SocialLoginButton
          provider="google"
          onClick={() => handleGoogleSignIn()}
          disabled={isLoading}
          label={googleLabel}
        />
        <SocialLoginButton
          provider="apple"
          onClick={() => handleAppleSignIn()}
          disabled={isLoading}
          label={appleLabel}
        />
        <LocaleSwitcher />
      </Box>
    </Stack>
  );
};

export default SignInForm;
