import { Box, Typography } from '@mui/material';
import { Comment as CommentType } from '@minicardiac-client/types';
import CommentHeader from './CommentHeader';
import { Profile } from '../navigations/Profile';

const ReplyItem = ({ reply }: { reply: CommentType }) => (
  <Box display="flex" gap="12px">
    <Profile
      displayName={reply.user.name}
      photoURL={reply.user.profilePic}
      size={32}
    />
    <Box flex={1}>
      <CommentHeader
        name={reply.user.name}
        postedAgo={reply.postedAgo}
        nameFontSize="14px"
        timeFontSize="12px"
      />
      <Typography mt="4px" fontSize="13px" fontWeight={400}>
        {reply.comment}
      </Typography>
    </Box>
  </Box>
);

export default ReplyItem;
