'use client';

import { Box, Typography, Stack, useMediaQuery } from '@mui/material';
import { LoadingButton } from '../loading-button';
import CustomDialog from './CustomDialog';
import ScheduleDateTimePicker from './ScheduleDateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { useState } from 'react';
import dayjs, { Dayjs } from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);
import { BackButton } from '../buttons/Backbutton';
import { useTheme } from '@emotion/react';
import { useTranslations } from 'next-intl';

interface SchedulePostDialogProps {
  open: boolean;
  onClose: () => void;
  returnToDialog?: string | null;
  setReturnDialogOpen?: (val: boolean) => void;
  onSchedule?: (isoDate: string) => void;
}

const SchedulePostDialog = ({
  open,
  onClose,
  returnToDialog,
  setReturnDialogOpen,
  onSchedule,
}: SchedulePostDialogProps) => {
  const t = useTranslations('schedulePost');

  const [selectedDate, setSelectedDate] = useState<Dayjs | null>(dayjs());
  const [hour, setHour] = useState('');
  const [minute, setMinute] = useState('');
  const [meridian, setMeridian] = useState<'AM' | 'PM'>('PM');
  const [timeFormat, setTimeFormat] = useState<'12h' | '24h'>('12h');

  const theme: any = useTheme();
  const screenBelowSM = useMediaQuery(theme.breakpoints.down('sm'));

  const isScheduleEnabled =
    selectedDate !== null &&
    hour.trim() !== '' &&
    minute.trim() !== '' &&
    /^\d{1,2}$/.test(hour) &&
    /^\d{1,2}$/.test(minute) &&
    (timeFormat === '24h' || (timeFormat === '12h' && (meridian === 'AM' || meridian === 'PM')));

  const handlePost = () => {
    if (!isScheduleEnabled || !selectedDate) return;

    // Convert 12-hour format to 24-hour format if needed
    let hour24 = Number(hour);
    if (timeFormat === '12h') {
      if (meridian === 'PM' && hour24 !== 12) {
        hour24 += 12;
      } else if (meridian === 'AM' && hour24 === 12) {
        hour24 = 0;
      }
    }

    // Compose date/time in local timezone
    const localDate = selectedDate
      .hour(hour24)
      .minute(Number(minute))
      .second(0)
      .millisecond(0);

    // Debug logging
    console.log('Selected time:', { hour: hour24, minute: Number(minute), meridian, timeFormat });
    console.log('Local date object:', localDate.format('YYYY-MM-DD HH:mm:ss'));
    console.log('Local timezone offset:', localDate.utcOffset());

    // Convert to UTC and then to ISO string
    const utcDate = localDate.utc();
    console.log('UTC date object:', utcDate.format('YYYY-MM-DD HH:mm:ss'));
    console.log('Final ISO string:', utcDate.toISOString());

    onSchedule?.(utcDate.toISOString());
  };

  return (
    <CustomDialog
      open={open}
      onClose={onClose}
      title=""
      sx={{
        px: { xs: 0, sm: '110px' },
        pt: { xs: 0, sm: '154px' },
        alignItems: { xs: 'stretch', sm: 'start' },
        '.MuiDialog-paper': {
          maxHeight: { xs: '100%', sm: 'calc(100% - 64px' },
        },
      }}
    >
      <Box
        display="flex"
        flexDirection="column"
        gap={'40px'}
        sx={{
          backgroundColor: 'white',
          padding: { xs: '16px', sm: '20px', md: '20px', lg: '40px' },
        }}
      >
        <Box
          display="flex"
          justifyContent={{ xs: 'start', sm: 'space-between' }}
          gap={'16px'}
          alignItems="center"
        >
          {screenBelowSM && (
            <BackButton onClick={() => setReturnDialogOpen?.(true)} />
          )}

          <Typography
            sx={{
              fontFamily: 'Plus Jakarta Sans',
              fontWeight: 500,
              fontSize: { xs: '20px', sm: '28px' },
              color: '#1E1E1E',
              height: '35px',
            }}
          >
            {t('title')}
          </Typography>
        </Box>

        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <ScheduleDateTimePicker
            selectedDate={selectedDate}
            setSelectedDate={setSelectedDate}
            hour={hour}
            setHour={setHour}
            minute={minute}
            setMinute={setMinute}
            meridian={meridian}
            setMeridian={setMeridian}
            timeFormat={timeFormat}
            setTimeFormat={setTimeFormat}
          />
        </LocalizationProvider>

        <Stack
          direction={{ xs: 'column', sm: 'row' }}
          spacing={{ xs: '10px', sm: '20px' }}
          justifyContent={{ xs: 'center', sm: 'center' }}
          sx={{
            position: { xs: 'fixed', sm: 'static' },
            bottom: 0,
            left: 0,
            mt: { xs: '0px', sm: '40px' },
            width: { xs: '100%', sm: 'auto' },
            background: { xs: 'rgba(255,255,255,0.8)', sm: 'transparent' },
            backdropFilter: { xs: 'blur(20px)', sm: 'none' },
            padding: { xs: '20px', sm: 0 },
            boxShadow: { xs: '0 -4px 20px 0 rgba(0,0,0,0.1)', sm: 'none' },
          }}
        >
          <Box
            sx={{
              display: 'flex',
              width: '100%',
              justifyContent: 'center',
              alignItems: 'center',
              flexDirection: { xs: 'column', sm: 'row' },
              gap: '20px',
            }}
          >
            <LoadingButton
              variant="outlined"
              onClick={() => setReturnDialogOpen?.(true)}
              sx={{
                width: { xs: '156px' },
                height: '40px',
                backgroundColor: 'white',
                border: { xs: 'none', sm: '1px solid #A24295' },
                color: '#A24295',
                '&:hover': {
                  backgroundColor: 'secondary.light',
                },
                fontSize: '16px',
                fontWeight: 700,
              }}
            >
              {t('return')}
            </LoadingButton>

            <LoadingButton
              variant="outlined"
              onClick={handlePost}
              disabled={!isScheduleEnabled}
              sx={{
                width: { xs: '156px' },
                height: '40px',
                backgroundColor: 'secondary.main',
                color: 'white',
                '&:hover': {
                  backgroundColor: 'secondary.light',
                },
                fontSize: '16px',
                fontWeight: 700,
              }}
            >
              {t('schedule')}
            </LoadingButton>
          </Box>
        </Stack>
      </Box>
    </CustomDialog>
  );
};

export default SchedulePostDialog;
