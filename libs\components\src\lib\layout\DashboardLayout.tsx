'use client';
import { useEffect, useState, useCallback, useMemo } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useQueryClient } from '@tanstack/react-query';
import WelcomeOverlay from './WelcomeOverlay';
import ResponsiveLayout from './ResponsiveLayout';
import TopBar from '../search-bar/TopBar';
import RightSidebar from '../navigations/RightSidebar';
import TagsMainContent from '../tags/TagsMainContent';
import MainContent from './MainContent';
import ContentTextPost from '../content-posts/ContentTextPost';
import MobileTopBar from '../search-bar/MobileTopBar';
import { MobileSearchbar } from '../navigations/MobileSearchBar';
import ActivePostDialogRenderer from './ActivePostDialogRenderer';
import { useFeed } from '@minicardiac-client/apis';
// import { FullPageLoader } from '../full-page-loader';
import ContentQuestionPost from '../content-posts/ContentQuestionPost';
import ContentPollPost from '../content-posts/ContentPollPost';
import { ContentArticlePost } from '../content-posts/ContentArticlePost';
import ContentMediaPost from '../content-posts/ContentMediaPost';
import FullFolderSectionView from '../saved-posts/FullFolderSectionView';
import ConditionalDraggablePost from './ConditionalDraggablePost';
import { getCdnUrl, truncate } from '@minicardiac-client/utilities';
import { useFeedStore } from '../store/useFeedStore';
import { useFeedSearchStore } from '../store/useFeedSearchStore';
import Fade from '@mui/material/Fade';
import Box from '@mui/material/Box';
import { PostSkeletonCard } from '../content-posts/PostSkeletonCard';
import {
  FeedPostType,
  FeedSearchState,
  FeedState,
} from '@minicardiac-client/types';

export default function DashboardLayout() {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  // const hasFilter = searchParams?.has('filter');

  // State
  const [showWelcome, setShowWelcome] = useState(false);
  const fromSignup = searchParams?.get('fromSignup');
  const hashTagFilter = !!searchParams?.has('hashtag');
  const folderFilter = !!searchParams?.has('folder');

  const searchKeyword = useFeedSearchStore(
    (state: FeedSearchState) => state.searchKeyword
  );
  const postTypes = useFeedSearchStore(
    (state: FeedSearchState) => state.postTypes
  );

  // Fetch posts feed with refetch function
  const {
    data: feedResponse,
    isLoading,
    // error,
    refetch: refetchFeed,
  } = useFeed({ postTypes, searchKeyword });

  // FIXED: Get feed data with proper validation and immediate postType setting
  const feed = useMemo(() => {
    if (!feedResponse) {
      return [];
    }

    if (Array.isArray(feedResponse)) {
      return feedResponse.map((post) => ({
        ...post,
        postType: post.postType || 'text',
      }));
    }

    if (feedResponse.data && Array.isArray(feedResponse.data)) {
      return feedResponse.data.map((post) => ({
        ...post,
        postType: post.postType || 'text', // Set default postType immediately
      }));
    }

    return [];
  }, [feedResponse]);

  // Handle post creation and refresh feed
  const handlePostCreated = useCallback(async () => {
    try {
      // Invalidate the feed query to force a refetch
      await queryClient.invalidateQueries({
        queryKey: ['feed'],
        refetchType: 'active',
        exact: true,
      });

      // Also explicitly refetch as a fallback
      await refetchFeed();

    } catch (error) {
      console.error('Error refreshing feed:', error);
    }
  }, [queryClient, refetchFeed]);

  // Handle welcome screen for new signups
  useEffect(() => {
    if (fromSignup === 'true') {
      setShowWelcome(true);
      window.history.replaceState({}, '', pathname);
    }
  }, [fromSignup, pathname]);

  const handleCloseWelcome = () => setShowWelcome(false);
  const isTagsPage = pathname?.includes('/feed/tags') ?? false;
  const isSavedPostsPage = pathname?.includes('/feed/saved-posts') ?? false;

  // Search Bar variants
  const topbarVariant = isTagsPage
    ? 'tags'
    : isSavedPostsPage
    ? 'saved-posts'
    : hashTagFilter || folderFilter
    ? 'filter'
    : 'default';

  const setFeed = useFeedStore((state: FeedState) => state.setFeed);

  useEffect(() => {
    if (feed?.length > 0) {
      setFeed(feed);
    }
  }, [feed, setFeed]);

  return (
    <>
      <WelcomeOverlay open={showWelcome} onClose={handleCloseWelcome} />

      <ResponsiveLayout
        topBar={
          isMobile ? (
            <>
              <MobileTopBar variant={topbarVariant} />
              <MobileSearchbar variant={topbarVariant} />
            </>
          ) : (
            <TopBar variant={topbarVariant} />
          )
        }
        rightSidebar={
          !isTagsPage && !isSavedPostsPage ? (
            <RightSidebar variant={folderFilter ? 'folders' : 'tags'} />
          ) : undefined
        }
      >
        {isTagsPage ? (
          <TagsMainContent />
        ) : isSavedPostsPage ? (
          <FullFolderSectionView />
        ) : (
          <MainContent>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: '16px',
              }}
            >
              {isLoading ? (
                [...Array(4)].map((_, idx) => <PostSkeletonCard key={idx} />)
              ) : (
                <Fade in={!isLoading} timeout={400}>
                  <div>
                    {feed?.map((post: FeedPostType) => {
                      // FIXED: Added safety check for post existence
                      if (!post || !post.id) {
                        return null;
                      }

                      //Safely Handling title
                      const title =
                        typeof post?.title === 'string'
                          ? post.title
                          : JSON.stringify(post?.title);
                      const truncatedTitle = truncate(title, 150);

                      // FIXED: postType is now guaranteed to be set from useMemo
                      const postType = post.postType; // Will be 'text' if originally undefined

                      if (postType === 'media') {
                        return (
                          <Box key={post?.id} sx={{ mb: 3 }}>
                            <ConditionalDraggablePost
                              postId={post?.id}
                              type="media"
                              folderFilter={folderFilter}
                            >
                              <ContentMediaPost
                                postId={post?.id}
                                title={truncatedTitle || ''}
                              />
                            </ConditionalDraggablePost>
                          </Box>
                        );
                      } else if (postType === 'article') {
                        return (
                          <Box key={post?.id} sx={{ mb: 3 }}>
                            <ConditionalDraggablePost
                              postId={post?.id}
                              type="article"
                              folderFilter={folderFilter}
                            >
                              <ContentArticlePost
                                postId={post?.id}
                                title={truncatedTitle || ''}
                                content={post?.content}
                                summary={post?.content?.slice(0, 200)}
                                coverImage={
                                  post?.coverImagePath
                                    ? getCdnUrl(post.coverImagePath)
                                    : undefined
                                }
                                user={{
                                  name: post?.publisherName || 'Anonymous',
                                  profilePic: post?.profileImageUrlThumbnail
                                    ? getCdnUrl(post?.profileImageUrlThumbnail)
                                    : '/placeholder-avatar.png',
                                  postedAgo: post?.postedAt
                                    ? new Date(
                                        post?.postedAt
                                      ).toLocaleDateString()
                                    : '',
                                }}
                                likes={post?.likesCount}
                                isLiked={post?.isLiked}
                                comments={post?.commentsCount}
                                reposts={post?.repostCount}
                                shares={post?.shareCount}
                              />
                            </ConditionalDraggablePost>
                          </Box>
                        );
                      } else if (postType === 'poll') {
                        return (
                          <Box key={post?.id} sx={{ mb: 3 }}>
                            <ConditionalDraggablePost
                              postId={post?.id}
                              type="poll"
                              folderFilter={folderFilter}
                            >
                              <ContentPollPost title={truncatedTitle || ''} />
                            </ConditionalDraggablePost>
                          </Box>
                        );
                      } else if (postType === 'question') {
                        return (
                          <Box key={post?.id} sx={{ mb: 3 }}>
                            <ConditionalDraggablePost
                              postId={post?.id}
                              type="question"
                              folderFilter={folderFilter}
                            >
                              <ContentQuestionPost
                                title={truncatedTitle || ''}
                                post={post}
                              />
                            </ConditionalDraggablePost>
                          </Box>
                        );
                      }
                      // Default to text post (this handles postType === 'text' and any other values)
                      return (
                        <Box key={post?.id} sx={{ mb: 3 }}>
                          <ConditionalDraggablePost
                            postId={post?.id}
                            type="text"
                            folderFilter={folderFilter}
                          >
                            <ContentTextPost
                              id={post?.id}
                              title={truncatedTitle || ''}
                              user={{
                                name: post?.publisherName || 'Anonymous',
                                profilePic: post?.profileImageUrlThumbnail
                                  ? getCdnUrl(post?.profileImageUrlThumbnail)
                                  : '/placeholder-avatar.png',
                                postedAgo: post?.postedAt
                                  ? new Date(
                                      post?.postedAt
                                    ).toLocaleDateString()
                                  : '',
                              }}
                              content={post?.content}
                              likes={post?.likesCount}
                              isLiked={post?.isLiked}
                              comments={post?.commentsCount}
                              reposts={post?.repostCount}
                              shares={post?.shareCount}
                              postId={post.id ?? ''}
                            />
                          </ConditionalDraggablePost>
                        </Box>
                      );
                    })}
                    <Box sx={{ mb: 3 }}>
                      <ConditionalDraggablePost
                        postId={`post-1-poll`}
                        type="poll"
                        folderFilter={folderFilter}
                      >
                        <ContentPollPost />
                      </ConditionalDraggablePost>
                    </Box>
                  </div>
                </Fade>
              )}
            </Box>
          </MainContent>
        )}
      </ResponsiveLayout>

      <ActivePostDialogRenderer onPostCreated={handlePostCreated} />
    </>
  );
}
