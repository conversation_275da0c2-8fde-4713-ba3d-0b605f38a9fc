'use client';

import { useRouter } from 'next/navigation';
import { Box, Card, Stack, Typography, Avatar } from '@mui/material';
import { MEDIA_POSTS } from '../auth';

const MOCK_IMAGES = MEDIA_POSTS;

// Mock data for content and id
const content = `Performed a challenging mitral valve repair today on a patient`;
const mediaId = '12312893';

export default function PostCard() {
  const router = useRouter();

  const handleClick = () => {
    // Generate slug
    const slug = content
      .toLowerCase()
      .trim()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '');

    router.push(`/feed/media/${slug}-${mediaId}`);
  };

  return (
    <Card
      onClick={handleClick}
      sx={{
        width: '205px',
        minWidth: '205px',
        minHeight: '166px',
        borderRadius: '8px',
        boxShadow: 'none',
        backgroundColor: 'white',
        padding: '12px',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        gap: '8px',
        cursor: 'pointer',
      }}
    >
      {/* Header */}
      <Stack direction="row" alignItems="center" spacing={1}>
        <Avatar
          src="https://via.placeholder.com/24"
          alt="Roger Taylor"
          sx={{ width: 24, height: 24 }}
        />
        <Typography fontSize="12px" fontWeight={600}>
          Roger Taylor
        </Typography>
      </Stack>

      {/* Content */}
      <Typography
        fontSize="12px"
        fontWeight={400}
        sx={{
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        }}
      >
        {content}
      </Typography>

      {/* Image Preview Strip */}
      <Box
        sx={{
          display: 'flex',
          overflow: 'hidden',
          position: 'relative',
          mt: '4px',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            gap: '12px',
            overflowX: 'auto',
            pr: '16px',
            '&::-webkit-scrollbar': {
              display: 'none',
            },
          }}
        >
          {MOCK_IMAGES.map((img, i) => (
            <Box
              key={i}
              component="img"
              src={img}
              alt={`img-${i}`}
              sx={{
                width: '70px',
                height: '66px',
                objectFit: 'cover',
                borderRadius: '4px',
                flexShrink: 0,
              }}
            />
          ))}
        </Box>

        {/* Fade effect */}
        <Box
          sx={{
            position: 'absolute',
            right: -1,
            top: 0,
            bottom: 0,
            width: '20px',
            background: `linear-gradient(to right, rgba(255,255,255,0) 0%, #F8F9FA 80%)`,
            pointerEvents: 'none',
          }}
        />
      </Box>
    </Card>
  );
}
