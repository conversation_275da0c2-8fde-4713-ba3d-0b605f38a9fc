export interface Comment {
  id: string;
  user: {
    name: string;
    profilePic: string;
  };
  postedAgo: string;
  comment: string;
  replies?: Comment[];
  repliesCount: number;
  createdAt?: string;
}

export interface CommentsListProps {
  comments: Comment[];
}

export interface AnswerComment {
  id: string;
  comment: string;
  content?: string;
  isPinned: boolean;
  creator: {
    id: string;
    username: string;
    displayName: string;
    profileImageUrlThumbnail: string;
  };
  repliesCount: number;
  createdAt: string;
}
