'use client';

import { forwardRef, useState } from 'react';
import { Box, Divider, Typography } from '@mui/material';
import PostHeader from './PostHeader';
import PostFooterActions from './PostFooterActions';
import { useRouter } from 'next/navigation';
import { ArticlePostProps } from '@minicardiac-client/types';
import { FullPageLoader } from '../full-page-loader';
import { MEDIA_1 } from '../auth';
import { useTranslations } from 'next-intl';
import HighlightText from '../common/HighlightText';
import { HighlightHtml } from '../common/HighlightHtml';
import { useFeedSearchStore } from '../store/useFeedSearchStore';
import { generateSlug } from '@minicardiac-client/utilities';
import { PostTags } from '../tags/PostTags';

export const ContentArticlePost = forwardRef(function ContentArticlePost(
  {
    user = {
      name: '<PERSON>',
      profilePic: '/placeholder-avatar.png',
      postedAgo: 'just now',
    },
    articleId = '7338905434222669825',
    title = 'Cardionex: A Breakthrough in Preventing Recurrent Cardiac Events',
    summary = `Early results from Phase III trials suggest Cardionex, a new oral therapy targeting vascular inflammation, significantly reduces the risk of recurrent heart attacks and major cardiac events. With its unique dual-action approach, Cardionex could soon redefine secondary prevention in cardiology.Early results from Phase III trials suggest Cardionex, a new oral therapy targeting vascular inflammation, significantly reduces the risk of recurrent heart attacks and major cardiac events. With its unique dual-action approach, Cardionex could soon redefine secondary prevention in cardiology.Early results from Phase III trials suggest Cardionex, a new oral therapy targeting vascular inflammation, significantly reduces the risk of recurrent heart attacks and major cardiac events. With its unique dual-action approach, Cardionex could soon redefine secondary prevention in cardiology.Early results from Phase III trials suggest Cardionex, a new oral therapy targeting vascular inflammation, significantly reduces the risk of recurrent heart attacks and major cardiac events. With its unique dual-action approach, Cardionex could soon redefine secondary prevention in cardiology.`,
    content = ``,
    coverImage = MEDIA_1,
    likes = 150,
    comments = 20,
    reposts = 8,
    shares = 3,
    isLiked = false,
    postId,
    ghost = false,
    tags,
    ...eventHandlers
  }: ArticlePostProps & {
    postId?: string;
    ghost?: boolean;
    isLiked?: boolean;
    onLikeChange?: (isLiked: boolean) => void;
    tags?: string[];
  } & React.HTMLAttributes<HTMLDivElement>,
  ref: React.Ref<HTMLDivElement>
) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const [showComments, setShowComments] = useState(false);

  const t = useTranslations('articlePost');
  const searchKeyword = useFeedSearchStore((state) => state.searchKeyword);

  const handleNavigate = () => {
    setLoading(true);
    const slug = generateSlug(title);

    router.push(`/feed/article/${postId}/${slug}`);
  };

  const handleCommentClick = () => {
    setShowComments((prev) => !prev);
  };

  return (
    <>
      <FullPageLoader
        open={loading}
        message={t('loading')}
        sx={{ backgroundColor: '#1E1E1E40' }}
      />

      <Box
        ref={ref}
        {...eventHandlers}
        sx={{
          width: '100%',
          p: { xs: '16px', sm: '20px' },
          borderRadius: '12px',
          backgroundColor: '#fff',
          boxShadow: '0px 1px 6px rgba(0, 0, 0, 0.05)',
          boxSizing: 'border-box',
          cursor: eventHandlers.onMouseDown ? 'grab' : 'default',
        }}
      >
        <PostHeader user={user} showOptions />

        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            gap: '20px',
            mt: '16px',
          }}
        >
          <Box >
            <Box
              component="img"
              src={coverImage}
              alt="article-thumbnail"
              width={{ xs: '100%', sm: '345px' }}
              height={{ xs: '312px', sm: '220px' }}
              sx={{
                objectFit: 'cover',
                borderRadius: '8px',
                cursor: 'pointer',
              }}
              onClick={handleNavigate}
            />
            
          </Box>
          <Box sx={{ flex: 1 }}>
            <Typography
              sx={{
                fontSize: '16px',
                fontWeight: 700,
                color: '#1E1E1E',
                lineHeight: '22px',
              }}
            >
              <HighlightText text={title} keyword={searchKeyword} />
            </Typography>
            <Typography
              component="div"
              sx={{
                fontSize: '12px',
                fontWeight: 400,
                color: '#1E1E1E',
                lineHeight: '20px',
                mt: 2,
              }}
            >
              <HighlightHtml html={summary} keyword={searchKeyword} />
            </Typography>

           

            <Typography
              sx={{
                fontSize: '12px',
                fontWeight: 500,
                color: '#A24295',
                cursor: 'pointer',
                userSelect: 'none',
                display: 'inline-block',
              }}
              onClick={handleNavigate}
            >
              {t('readFull')} →
            </Typography>
            
          </Box>
        
        </Box>
        <PostTags tags={tags || []} />
        <Divider
          sx={{ mt: '12px', mb: '8px', borderColor: '#A3A3A3', opacity: 0.5 }}
        />

        <PostFooterActions
          likes={likes}
          isLiked={isLiked}
          commentsCount={comments}
          reposts={reposts}
          shares={shares}
          onOpenComments={handleCommentClick}
          showComments={showComments}
          setShowComments={setShowComments}
          postId={postId}
        />
      </Box>
    </>
  );
});

