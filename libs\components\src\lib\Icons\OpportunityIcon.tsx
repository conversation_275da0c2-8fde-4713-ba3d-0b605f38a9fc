import { useState } from 'react';

const OpportunityIcon = ({
  fill = '#A3A3A3',
  hoverFill = '#A24295',
  size = 24,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const currentFill = isHovered ? hoverFill : fill;

  return (
    <svg
      width="22"
      height="24"
      viewBox="0 0 22 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <path
        d="M20.2096 15.4429C19.7534 15.1695 19.2174 15.0898 18.7019 15.2182C18.3235 15.3125 17.9889 15.5119 17.7292 15.7921L14.6934 13.9692C15.0125 13.3447 15.1934 12.6385 15.1934 11.8895C15.1934 11.2184 15.0475 10.5822 14.7878 10.0073L17.7536 8.2535C18.1359 8.66885 18.6728 8.89548 19.2214 8.89548C19.5657 8.89548 19.915 8.80599 20.2321 8.61825C21.1766 8.05894 21.4908 6.83525 20.9324 5.89078C20.662 5.43264 20.2291 5.10775 19.7136 4.97545C19.1981 4.84316 18.6621 4.92 18.2049 5.19042C17.383 5.67677 17.0386 6.66604 17.3314 7.53759L14.3793 9.28362C13.6245 8.19417 12.4115 7.44615 11.0206 7.32068V3.93854C11.9194 3.74691 12.5964 2.94733 12.5964 1.99116C12.5964 0.892947 11.7035 0 10.6052 0C9.50703 0 8.61408 0.892947 8.61408 1.99116C8.61408 2.94636 9.29111 3.74595 10.1899 3.93854V7.32068C8.80959 7.44518 7.60435 8.18347 6.84859 9.25932L3.90795 7.49384C4.20657 6.62423 3.86905 5.63205 3.05002 5.14084C2.10844 4.5757 0.882807 4.88209 0.317565 5.82369C-0.247676 6.76529 0.0588126 7.9909 0.999446 8.55614C1.31363 8.74485 1.66479 8.84114 2.02081 8.84114C2.18228 8.84114 2.3457 8.82169 2.50716 8.78083C2.88652 8.68648 3.22017 8.48707 3.47987 8.20693L6.43295 9.9802C6.16546 10.5619 6.01468 11.2087 6.01468 11.8906C6.01468 12.6279 6.19074 13.3254 6.50103 13.944L3.45457 15.7465C3.19679 15.4654 2.86316 15.264 2.48575 15.1668C1.97021 15.0345 1.43423 15.1113 0.977059 15.3817C0.0315702 15.941 -0.281642 17.1647 0.276702 18.1092C0.547108 18.5673 0.979973 18.8922 1.49551 19.0245C1.66087 19.0664 1.82817 19.0878 1.99354 19.0878C2.34568 19.0878 2.69293 18.9934 3.0042 18.8096C3.46234 18.5392 3.78723 18.1063 3.91953 17.5907C4.0168 17.2124 4.00027 16.8233 3.87771 16.4624L6.93986 14.6512C7.69956 15.658 8.8639 16.3418 10.1888 16.4614V20.0615C9.28997 20.2531 8.61297 21.0527 8.61297 22.0088C8.61297 23.1071 9.50592 24 10.6041 24C11.7023 24 12.5953 23.1071 12.5953 22.0088C12.5953 21.0536 11.9183 20.2531 11.0195 20.0615V16.4614C12.3336 16.3428 13.4892 15.6677 14.25 14.6745L17.3014 16.5062C17.1769 16.8671 17.1584 17.2561 17.2528 17.6345C17.3812 18.151 17.7041 18.5858 18.1593 18.8592C18.4794 19.0518 18.8325 19.1432 19.1807 19.1432C19.8577 19.1432 20.5182 18.7989 20.8917 18.1773C21.4569 17.2357 21.1505 16.0101 20.2089 15.4449L20.2096 15.4429ZM18.627 5.90531C19.1775 5.57945 19.8906 5.76232 20.2164 6.31287C20.5423 6.86342 20.3594 7.57643 19.8089 7.90229C19.2583 8.22816 18.5453 8.04528 18.2194 7.49376C17.8936 6.94321 18.0764 6.2302 18.627 5.90434V5.90531ZM2.30573 7.97331C2.00517 8.04821 1.69293 8.00249 1.42738 7.842C0.878773 7.51322 0.700751 6.79827 1.02953 6.24966C1.24644 5.88782 1.63165 5.68646 2.02656 5.68646C2.22986 5.68646 2.4351 5.73996 2.62186 5.85182C3.17047 6.1806 3.34849 6.89555 3.01971 7.44416C2.86019 7.70971 2.6063 7.89744 2.30573 7.97235V7.97331ZM3.11504 17.3824C3.03819 17.6829 2.84852 17.9349 2.58199 18.0925C2.03144 18.4183 1.31844 18.2355 0.992573 17.6849C0.834993 17.4184 0.790248 17.1062 0.867092 16.8056C0.943936 16.505 1.13361 16.2531 1.40014 16.0955C1.58106 15.9885 1.7834 15.9331 1.98864 15.9331C2.08591 15.9331 2.18318 15.9457 2.27949 15.97C2.58005 16.0469 2.832 16.2365 2.98958 16.5031C3.14716 16.7696 3.19188 17.0818 3.11504 17.3824ZM9.44453 1.99095C9.44453 1.3509 9.96492 0.830484 10.605 0.830484C11.245 0.830484 11.7655 1.35088 11.7655 1.99095C11.7655 2.63099 11.2451 3.15141 10.605 3.15141C9.96495 3.15141 9.44453 2.63102 9.44453 1.99095ZM10.605 15.647C9.87351 15.647 9.19066 15.4359 8.61286 15.0731C8.7257 14.3805 9.12452 13.8475 9.5603 13.8475H11.6867C12.1215 13.8475 12.5144 14.363 12.6302 15.0526C12.0456 15.4281 11.3501 15.6479 10.605 15.6479L10.605 15.647ZM11.7645 22.0066C11.7645 22.6467 11.2441 23.1671 10.604 23.1671C9.96393 23.1671 9.44353 22.6467 9.44353 22.0066C9.44353 21.3666 9.96393 20.8461 10.604 20.8461C11.2441 20.8461 11.7645 21.3665 11.7645 22.0066ZM13.3403 14.4622C13.0591 13.5945 12.4298 13.0167 11.6857 13.0167H9.5593C8.81614 13.0167 8.17609 13.6159 7.89789 14.4923C7.24811 13.8163 6.84637 12.899 6.84637 11.8894C6.84637 9.81748 8.53208 8.13171 10.604 8.13171C12.676 8.13171 14.3617 9.81743 14.3617 11.8894C14.3617 12.8845 13.9726 13.7891 13.3393 14.4622L13.3403 14.4622ZM20.1795 17.7469C19.8497 18.2956 19.1358 18.4736 18.5872 18.1448C18.3216 17.9853 18.1339 17.7314 18.059 17.4308C17.9841 17.1303 18.0308 16.818 18.1903 16.5525C18.3498 16.2869 18.6037 16.0992 18.9043 16.0243C18.9986 16.0009 19.093 15.9893 19.1873 15.9893C19.3955 15.9893 19.5997 16.0457 19.7826 16.1556C20.3312 16.4844 20.5092 17.1993 20.1805 17.7479L20.1795 17.7469Z"
        fill={currentFill}
      />
      <path
        d="M10.5776 8.97266C9.55236 8.97266 8.71875 9.80629 8.71875 10.8315C8.71875 11.8568 9.55238 12.6904 10.5776 12.6904C11.6029 12.6904 12.4365 11.8567 12.4365 10.8315C12.4365 9.80626 11.6028 8.97266 10.5776 8.97266ZM10.5776 11.8597C10.0105 11.8597 9.54944 11.3987 9.54944 10.8316C9.54944 10.2645 10.0105 9.80342 10.5776 9.80342C11.1447 9.80342 11.6058 10.2645 11.6058 10.8316C11.6058 11.3987 11.1447 11.8597 10.5776 11.8597Z"
        fill={currentFill}
      />
      <circle cx="2.03365" cy="17.0864" r="1.36959" fill={currentFill} />
      <circle cx="10.4946" cy="22.0688" r="1.36959" fill={currentFill} />
      <circle cx="19.2133" cy="17.0883" r="1.36959" fill={currentFill} />
      <circle cx="19.2133" cy="6.8774" r="1.36959" fill={currentFill} />
      <circle cx="10.4946" cy="1.89693" r="1.36959" fill={currentFill} />
      <path
        d="M14.3591 11.8568C14.3591 13.9197 12.6888 15.7166 10.6259 15.7166C8.56295 15.7166 6.89062 14.0443 6.89062 11.9813C6.89062 9.91842 8.56295 8.24609 10.6259 8.24609C12.6888 8.24609 14.3591 9.79391 14.3591 11.8568Z"
        fill="white"
      />
      <circle cx="2.03365" cy="6.8774" r="1.36959" fill={currentFill} />
      <path
        d="M15 12C15 14.4853 12.9853 16.5 10.5 16.5C8.01472 16.5 6 14.4853 6 12C6 9.51472 8.01472 7.5 10.5 7.5C12.9853 7.5 15 9.51472 15 12Z"
        fill={currentFill}
      />
    </svg>
  );
};

export default OpportunityIcon;
