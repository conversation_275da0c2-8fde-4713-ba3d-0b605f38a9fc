import { useQuery } from '@tanstack/react-query';
import { postApi } from './post-api.js';
import type { FeedPost } from './types.js';
import { FeedPostType } from '@minicardiac-client/types';

export interface FeedResponse {
  data: FeedPost[];
  total: number;
  limit: number;
  offset: number;
}

interface FeedQueryOptions {
  postTypes?: string[];
  limit?: number;
  offset?: number;
  searchKeyword?: string;
}

const allowedPostTypes = [
  'question',
  'poll',
  'article',
  'media',
  'text',
] as const;
type AllowedPostType = (typeof allowedPostTypes)[number];

export function useFeed(options: FeedQueryOptions = {}) {
  const {
    postTypes = ['text', 'question'],
    limit = 25,
    offset = 0,
    searchKeyword = '',
  } = options;

  return useQuery<FeedResponse>({
    queryKey: ['feed', { postTypes, limit, offset, searchKeyword }],
    queryFn: async () => {
      const response = await postApi.getFeed({
        postTypes,
        limit,
        offset,
        searchKeyword,
      });

      return response as unknown as FeedResponse;
    },
    select: (data: FeedResponse) => ({
      ...data,
      data: data.data.map((post) => ({
        ...post,
        isLiked: post.isLiked || false,
        likesCount: post.likesCount || 0,
        commentsCount: post.commentsCount || 0,
        shareCount: post.shareCount || 0,
        repostCount: post.repostCount || 0,
        publisherName: post.publisherName || post.username || 'Anonymous',
        title: post.title || '',
      })),
    }),
  });
}

// Hook to fetch a single post by ID
export function usePostById(postId?: string) {
  return useQuery<FeedPostType>({
    queryKey: ['post', postId],
    queryFn: async () => {
      if (!postId) throw new Error('Post ID is required');

      const response = await postApi.getPostById(postId);

      const validatedPostType: AllowedPostType = allowedPostTypes.includes(
        response.postType
      )
        ? response.postType
        : (() => {
            console.warn(
              `Unexpected post type: ${response.postType}, defaulting to 'text'`
            );
            return 'text';
          })();

      return {
        ...response,
        postType: validatedPostType,
        isLiked: response.isLiked ?? false,
        likesCount: response.likesCount ?? 0,
        commentsCount: response.commentsCount ?? 0,
        repostCount: response.repostCount ?? 0,
        shareCount: response.shareCount ?? 0,
        publisherName:
          response.publisherName || response.username || 'Anonymous',
        title: response.title ?? '',
      };
    },
    enabled: !!postId,
  });
}
