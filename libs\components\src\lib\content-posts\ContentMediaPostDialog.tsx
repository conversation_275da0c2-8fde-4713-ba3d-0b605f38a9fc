'use client';

import { useCallback, useState } from 'react';
import {
  Box,
  Typography,
  IconButton,
  Divider,
  useMediaQuery,
} from '@mui/material';
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import CloseIcon from '@mui/icons-material/Close';

import CustomDialog from '../dialogs/CustomDialog';
import PostHeader from './PostHeader';
import { Iconify } from '../iconify';
import LikeIcon from '../Icons/ContentPostIcons/LikeIcon';
import ContentMediaPostMobile from './ContentMediaPostMobile';
import { useTheme } from '@emotion/react';
import CommentsList from '../comments/CommentsList';
import AddCommentInput from '../comments/AddCommentInput';
import { useTranslations } from 'next-intl';
import { useComments, useLikePost } from '@minicardiac-client/apis';
import LikeIconSelected from '../Icons/ContentPostIcons/LikeIconSelected';
import { useFeedStore } from '../store/useFeedStore';

interface ContentMediaPostDialogProps {
  open: boolean;
  onClose: () => void;
  media: string[];
  user: {
    name: string;
    profilePic: string;
    postedAgo: string;
  };
  content: string;
  likes: number;
  reposts: number;
  shares: number;
  postId: string;
  isLiked: boolean;
}

const ContentMediaPostDialog = ({
  open,
  onClose,
  media,
  user,
  content,
  likes,
  reposts,
  shares,
  postId,
  isLiked = false,
}: ContentMediaPostDialogProps) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const theme: any = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));
  const t = useTranslations('mediaPost');

  const [liked, setLiked] = useState(isLiked);
  const [likeCount, setLikeCount] = useState(likes);
  const [reposted, setReposted] = useState(false);
  const [repostCount, setRepostCount] = useState(reposts);

  const likeMutation = useLikePost();

  const handleLikeChange = useCallback(
    (postId: string, like: boolean) => {
      useFeedStore.getState().updateLike(postId, like);

      likeMutation.mutate(
        { postId, like },
        {
          onError: (error) => {
            console.error('Failed to like post:', error);
            useFeedStore.getState().updateLike(postId, !like);
            setLiked(!like);
            setLikeCount((prev) => prev + (like ? -1 : 1));
          },
        }
      );
    },
    [likeMutation]
  );

  const handleRepostToggle = () => {
    const newReposted = !reposted;
    setReposted(newReposted);
    setRepostCount((prev) => prev + (newReposted ? 1 : -1));
  };

  const handlePrev = () =>
    setCurrentIndex((prev) => (prev === 0 ? media.length - 1 : prev - 1));

  const handleNext = () =>
    setCurrentIndex((prev) => (prev === media.length - 1 ? 0 : prev + 1));

  const { comments: fetchedComments, isLoading: isCommentsLoading } =
    useComments(postId || '', { enabled: !!postId });

  if (isSmallScreen) {
    return (
      <ContentMediaPostMobile
        open={open}
        onClose={onClose}
        media={media}
        user={user}
        content={content}
        likes={likes}
        commentList={fetchedComments}
      />
    );
  }

  return (
    <CustomDialog
      open={open}
      onClose={onClose}
      sx={{
        '& .MuiPaper-root': {
          width: '100%',
          maxWidth: 'none',
          maxHeight: '740px',
          height: '100%',
          margin: 0,
          backgroundColor: '#fff',
          boxSizing: 'border-box',
        },
        paddingX: { xs: '0px', sm: '20px', md: '60px' },
        paddingTop: { xs: '0px', sm: '45px' },
      }}
    >
      <Box
        width="100%"
        display="flex"
        flexDirection={{ xs: 'column', sm: 'row' }}
        bgcolor="#fff"
        height={{ xs: '100vh', sm: '100%' }}
        p={{ xs: '0px', sm: '40px' }}
        gap="20px"
        borderRadius="8px"
        boxSizing="border-box"
      >
        {/* Media Section */}
        <Box
          position="relative"
          width={{ xs: '100%', sm: '58%' }}
          height="100%"
          maxHeight="740px"
          bgcolor="#000"
          display="flex"
          justifyContent="center"
          alignItems="center"
          sx={{ borderRadius: '8px', overflow: 'hidden' }}
        >
          <Box
            component="img"
            src={media[currentIndex]}
            alt={`media-${currentIndex}`}
            sx={{
              maxWidth: '100%',
              maxHeight: '100%',
              height: '660px',
              objectFit: 'contain',
              borderRadius: '8px',
              display: 'block',
              margin: 'auto',
            }}
          />
          {!isSmallScreen && media.length > 1 && (
            <>
              <IconButton
                onClick={handlePrev}
                sx={{ position: 'absolute', left: '8px', color: '#fff' }}
              >
                <ArrowBackIosNewIcon sx={{ fontSize: 36 }} />
              </IconButton>
              <IconButton
                onClick={handleNext}
                sx={{ position: 'absolute', right: '8px', color: '#fff' }}
              >
                <ArrowForwardIosIcon sx={{ fontSize: 36 }} />
              </IconButton>
            </>
          )}
        </Box>

        {/* Content Section */}
        <Box
          width={{ xs: '100%', sm: '42%' }}
          height="100%"
          display="flex"
          flexDirection="column"
          boxSizing="border-box"
          position="relative"
          sx={{ borderTopRightRadius: '8px', borderBottomRightRadius: '8px' }}
        >
          {/* Header */}
          <Box display="flex" justifyContent="space-between" px="8px" pt="8px">
            <PostHeader user={user} showOptions={false} />
            <Box display="flex" gap="20px">
              <IconButton sx={{ p: 0, color: '#A24295' }}>
                <MoreHorizIcon sx={{ fontSize: 36 }} />
              </IconButton>
              <IconButton onClick={onClose} sx={{ p: 0, color: '#A24295' }}>
                <CloseIcon sx={{ fontSize: 36 }} />
              </IconButton>
            </Box>
          </Box>

          {/* Main Content */}
          <Box
            flex={1}
            overflow="auto"
            pr="4px"
            sx={{
              scrollbarWidth: 'none', // Firefox
              msOverflowStyle: 'none', // IE/Edge
              '&::-webkit-scrollbar': {
                display: 'none', // Chrome/Safari
              },
            }}
          >
            <Box pb={2}>
              <Box
                mt="20px"
                lineHeight="22px"
                sx={{ fontSize: '16px', color: '#1E1E1E' }}
                dangerouslySetInnerHTML={{ __html: content }}
              />
              <Divider sx={{ mt: '12px', mb: '8px' }} />

              {/* Reactions */}
              <Box display="flex" justifyContent="space-between" mb="20px">
                <Typography fontSize="12px" fontWeight={600}>
                  {t('comments')} ({fetchedComments.length})
                </Typography>
                <Box display="flex" gap="28px">
                  <Box
                    display="flex"
                    alignItems="center"
                    gap="4px"
                    sx={{ cursor: 'pointer' }}
                    onClick={() => handleLikeChange(postId, liked)}
                  >
                    {liked ? <LikeIconSelected /> : <LikeIcon />}
                    <Typography
                      fontSize="12px"
                      fontWeight={600}
                      color="#A24295"
                    >
                      {t('like')} ({likeCount})
                    </Typography>
                  </Box>
                  <Box
                    display="flex"
                    alignItems="center"
                    gap="4px"
                    sx={{ cursor: 'pointer', opacity: reposted ? 0.5 : 1 }}
                    onClick={handleRepostToggle}
                  >
                    <Iconify
                      icon="garden:arrow-retweet-stroke-12"
                      sx={{ color: '#A24295', fontSize: 20 }}
                    />
                    <Typography
                      fontSize="12px"
                      fontWeight={600}
                      color="#A24295"
                    >
                      {t('repost')} ({repostCount})
                    </Typography>
                  </Box>
                  <Box display="flex" alignItems="center" gap="4px">
                    <Iconify
                      icon="mdi:share"
                      sx={{ color: '#A24295', fontSize: 20 }}
                    />
                    <Typography
                      fontSize="12px"
                      fontWeight={600}
                      color="#A24295"
                    >
                      {t('share')} ({shares})
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Box>

            <Box flex={1} display="flex" flexDirection="column" gap="8px">
              {isCommentsLoading ? (
                <Typography
                  fontSize="14px"
                  color="#888"
                  textAlign="center"
                  mt="8px"
                >
                  Loading comments...
                </Typography>
              ) : (
                <CommentsList
                  comments={fetchedComments}
                  postId={postId}
                  sx={{ mb: 0 }}
                />
              )}
            </Box>
          </Box>

          <Box p="8px" borderTop="1px solid #ddd">
            <AddCommentInput postId={postId} />
          </Box>
        </Box>
      </Box>
    </CustomDialog>
  );
};

export default ContentMediaPostDialog;
