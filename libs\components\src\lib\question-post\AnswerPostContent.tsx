import { AnswerComment } from '@minicardiac-client/types';
import { Box, Typography } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { postApi } from '@minicardiac-client/apis';
import { toast } from 'react-toastify';

import StarOutlineIcon from '@mui/icons-material/StarOutline';
import { useTranslations } from 'next-intl';
import { Profile } from '../navigations/Profile';
import { getTimeAgo } from '@minicardiac-client/utilities';

const AnswerPostContent = ({
  comment,
  questionId,
}: {
  comment: AnswerComment;
  questionId: string;
}) => {
  const t = useTranslations('questionPost');

  const { mutateAsync: unpinComment } = useMutation({
    mutationFn: async (questionId: string) => {
      await postApi.unpinComment(questionId);
    },
    onSuccess: () => {
      toast.success('Comment unpinned successfully');
    },
    onError: () => {
      toast.error('Failed to unpin the comment');
    },
  });

  return (
    <Box
      display="flex"
      gap="12px"
      flexDirection="column"
      px="20px"
      py="16px"
      bgcolor="#F3F4F6"
      borderRadius="8px"
      width="100%"
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        {/* Left: Avatar + Name */}
        <Box
          sx={{
            display: 'flex',
            gap: '8px',
            alignItems: 'center',
          }}
        >
          <Profile
            displayName={comment.creator.displayName}
            photoURL={comment.creator.profileImageUrlThumbnail}
            size={40}
          />
          <Box display="flex" alignItems="center" gap="6px">
            <Typography fontSize="16px" fontWeight={600}>
              {comment.creator.displayName}
            </Typography>

            {/* Featured Answer label */}
            <Box
              display={{ xs: 'none', sm: 'flex' }}
              alignItems="center"
              gap="4px"
            >
              <StarOutlineIcon sx={{ fontSize: 18, color: '#A24295' }} />
              <Typography fontSize="14px" fontWeight={500} color="#737678">
                {t('featuredAnswer')}
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Right: Unpin */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
          }}
        >
          {/* Optional: comment.postedAgo */}
          <Typography fontSize="14px" fontWeight={300} color="text.secondary">
            {getTimeAgo(comment.createdAt)}
          </Typography>

          <Typography
            fontSize="14px"
            fontWeight={500}
            color="#A24295"
            sx={{ cursor: 'pointer' }}
            onClick={() => unpinComment(questionId)}
          >
            {t('unpin')}
          </Typography>
        </Box>
      </Box>

      <Typography mt="8px" fontSize="14px" fontWeight={400}>
        {comment.comment || comment.content}
      </Typography>
    </Box>
  );
};

export default AnswerPostContent;
