'use client';
import {
  <PERSON>,
  CardContent,
  Typography,
  Button,
  Box,
  Avatar,
  alpha,
  Chip,
} from '@mui/material';
import Image from 'next/image';

import Iconify from '../../../iconify/iconify';
import { getAssetUrl } from '../../../utils/assetUtils';
import { getImageUrl, getInitials } from '@minicardiac-client/utilities';
import { BLANK_PROFILE } from '../../../auth/constants/auth.constants';

interface ProfileCardProps {
  id: string;
  name: string;
  qualification: string;
  segmentCategory: string;
  worksAt: string;
  rating: number;
  avatarUrl: string;
  sponsoredBy?: string;
  isConnected?: boolean;
  isFollowing?: boolean;
  connectionRequestSent?: boolean;
  onConnect?: (profileId: string) => void;
  onFollow?: (profileId: string) => void;
  connectionRequests?: Record<string, boolean>;
}

export default function ProfileCard({
  id,
  name,
  qualification,
  segmentCategory,
  worksAt,
  sponsoredBy,
  rating,
  avatarUrl,
  isConnected,
  isFollowing,
  connectionRequestSent,
  onConnect,
  onFollow,
  connectionRequests = {},
}: ProfileCardProps) {
  const hasPendingRequest = connectionRequests?.[id] || connectionRequestSent;

  const handleConnect = () => {
    console.log('ProfileCard: Connect clicked for ID:', id);
    console.log('Current connection state:', {
      isConnected,
      connectionRequestSent,
      hasPendingRequest,
    });
    if (onConnect) {
      onConnect(id);
    }
    console.log('After onConnect call - button should show "Request Sent"');
  };

  const handleFollow = () => {
    console.log('ProfileCard: Follow clicked for ID:', id);
    if (onFollow) {
      onFollow(id);
    }
  };

  const connectButtonText = connectionRequestSent
    ? 'Request Sent'
    : isConnected
    ? 'Connected'
    : 'Connect';

  const followButtonText = isFollowing ? 'Following' : 'Follow';

  return (
    <Card
      sx={{
        width: 320,
        height: 359,
        bgcolor: 'white',
        borderRadius: 1,
        position: 'relative',
        overflow: 'visible', // Changed from 'hidden' to 'visible'
        textAlign: 'center',
        boxShadow: (theme) =>
          `0px 12px 24px 0px ${alpha(
            (theme.palette as any).neutral[500],
            0.25
          )}`,
        '&::after': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          zIndex: -1,
          opacity: 0,
        },
      }}
    >
      <Box
        sx={{
          height: 64, // Changed from 80px to 64px to match intended height
          width: '100%',
          position: 'relative',
          overflow: 'hidden',
          borderTopLeftRadius: 12,
          borderTopRightRadius: 12,
        }}
      >
        <svg
          viewBox="0 0 1000 160"
          preserveAspectRatio="none"
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%', // Changed to 100% to ensure it fills the container width
            height: '100%', // Changed to 100% to ensure it fills the exact 64px height
          }}
        >
          <defs>
            <clipPath id="curveClip">
              <path
                d="
            M0,0
            H1000
            V160
            H850
            C600,160 690,55 500,43
            C320,49 400,160 150,160
            H0
            Z
          "
              />
            </clipPath>
          </defs>
          <image
            href={getAssetUrl(
              sponsoredBy
                ? '/assets/subscription/cardiac-specialist/Banner-2.svg'
                : '/assets/subscription/cardiac-specialist/Banner.svg'
            )}
            width="1000"
            preserveAspectRatio="xMidYMid slice"
            clipPath="url(#curveClip)"
          />
        </svg>
      </Box>

      {/* Avatar */}
      <Box sx={{ textAlign: 'center', mt: 4 }}>
        {avatarUrl && !getImageUrl(avatarUrl).includes('default-profile') ? (
          <Avatar
            src={getImageUrl(avatarUrl)}
            alt={name}
            sx={{
              width: 100,
              height: 100,
              position: 'absolute',
              top: 23,
              left: '50%',
              transform: 'translateX(-50%)',
              zIndex: 2,
              backgroundColor: '#fff',
            }}
          ></Avatar>
        ) : (
          <Box
            sx={{
              width: 100,
              height: 100,
              position: 'absolute',
              top: 23,
              left: '50%',
              transform: 'translateX(-50%)',
              zIndex: 2,
              backgroundColor: '#fff',
              borderRadius: '50%',
              overflow: 'hidden',
            }}
          >
            {/* Background image */}
            <Box
              component="img"
              src={BLANK_PROFILE}
              alt="Background pattern"
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                objectFit: 'cover',
                zIndex: 0,
                transform: 'scale(1.05)',
                transformOrigin: 'center',
              }}
            />
            <Box
              sx={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                zIndex: 1,
              }}
            >
              <Typography
                sx={{
                  fontSize: '1.5rem',
                  fontWeight: 500,
                  color: (theme) =>
                    (theme.palette as any).neutral?.[900] || '#1E1E1E',
                }}
              >
                {name && getInitials(name)}
              </Typography>
            </Box>
          </Box>
        )}
      </Box>
      {sponsoredBy && (
        <Image
          src={getAssetUrl(
            '/assets/subscription/cardiac-specialist/prestige-icon-small.svg'
          )}
          width={24}
          height={24}
          style={{
            position: 'absolute',
            top: 85,
            left: '60%',
            marginLeft: 3,
            zIndex: 3,
          }}
          alt="prestige-icon"
        />
      )}

      {/* Arc Rating */}
      <Box
        sx={{
          position: 'absolute',
          top: 144, // 👈 Adjust based on spacing
          left: '50%',
          width: 100,
          height: 50,
          transform: 'translateX(-50%)',
          pointerEvents: 'none',
        }}
      >
        {Array.from({ length: 5 }).map((_, i) => {
          const radius = 50; // Wider radius = gentler curve
          const centerX = 50; // Middle of Box
          const centerY = -60; // Push up to create smile arc (negative value)

          // Spread stars evenly but in reverse direction for smile
          const startAngle = Math.PI / 4; // ~45°
          const endAngle = Math.PI - Math.PI / 4; // ~135°
          const angleStep = (endAngle - startAngle) / 4;
          const angle = endAngle - i * angleStep;

          const x = centerX + radius * Math.cos(angle);
          const y = centerY + radius * Math.sin(angle);

          return (
            <Iconify
              key={i}
              width={16}
              icon={'material-symbols-light:kid-star-sharp'}
              sx={{
                position: 'absolute',
                left: `${x - 8}px`, // center 16px star
                top: `${y - 8}px`,
                color: (theme) =>
                  i + 1 <= rating
                    ? theme.palette.warning.main
                    : (theme.palette as any)?.neutral?.[500],
              }}
            />
          );
        })}
      </Box>
      <CardContent
        sx={{
          position: 'absolute',
          maxHeight: '190px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          top: '153px',
          width: 1,
          paddingTop: 0,
          textAlign: 'center',
          height: 1,
          zIndex: 1, // Ensure content is above card background
          visibility: 'visible !important', // Force visibility
          opacity: 1, // Ensure full opacity
          '&:last-child': {
            paddingBottom: 0,
          },
        }}
      >
        {/* Doctor Name */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            mb: 0.5,
          }}
        >
          <Typography
            variant="h5"
            sx={{
              lineHeight: 1,
            }}
            component="h2"
          >
            {name}
          </Typography>
          {!sponsoredBy && (
            <Image
              src={getAssetUrl(
                '/assets/subscription/cardiac-specialist/brand-icon.svg'
              )}
              width={20}
              height={19}
              style={{
                marginLeft: 3,
              }}
              alt="brand-icon"
            />
          )}
        </Box>

        {/* Credentials */}
        <Typography
          variant="caption"
          sx={{
            color: (theme) => (theme.palette as any).neutral[500],
            lineHeight: 1,
          }}
        >
          {qualification}
        </Typography>

        {/* Title */}
        <Typography variant={'subtitle3' as any} sx={{ mt: 1.5 }}>
          {segmentCategory}
        </Typography>

        {/* Hospital */}
        <Typography
          variant="caption"
          sx={{
            color: (theme) => (theme.palette as any).neutral[500],
          }}
        >
          {worksAt}
        </Typography>

        {sponsoredBy && (
          <Chip
            label={
              <Typography
                variant="caption"
                sx={{
                  color: (theme) => (theme.palette as any).neutral[900],
                }}
              >
                Prestige Sponsored by {sponsoredBy}
              </Typography>
            }
            size="small"
            sx={{
              borderRadius: 1,
              mt: 2,
              bgcolor: (theme) => alpha(theme.palette.secondary.main, 0.08),
              '& .MuiChip-label': {
                px: 1,
                py: 0.25,
              },
            }}
          />
        )}

        {/* Buttons */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            gap: 2,
            position: 'relative',
            bottom: 1,
            flexGrow: 1,
            alignItems: 'end',
            zIndex: 2,
            visibility: 'visible',
            opacity: 1,
          }}
        >
          <Button
            variant="outlined"
            onClick={handleConnect}
            disabled={isConnected}
            sx={(theme) => ({
              width: '136px',
              borderColor: connectionRequestSent
                ? 'black'
                : theme.palette.secondary.main,
              backgroundColor: 'transparent',
              visibility: 'visible',
              display: 'flex',
              zIndex: 10,
              whiteSpace: 'nowrap', // Prevent text wrapping
              '&:disabled': {
                borderColor: theme.palette.secondary.main,
                color: (theme.palette as any).neutral[900], // Using theme color for #333537
                opacity: connectionRequestSent ? 1 : 0.7,
                backgroundColor: connectionRequestSent
                  ? alpha(theme.palette.secondary.main, 0.08)
                  : 'transparent',
                visibility: 'visible !important',
                display: 'flex !important',
              },
            })}
          >
            <Typography
              variant={'professionalType' as any}
              color={connectionRequestSent ? 'text.primary' : 'secondary'}
              sx={{
                fontWeight: connectionRequestSent ? 700 : 500,
                fontSize: '16px',
                lineHeight: '24px',
                letterSpacing: '0px',
                display: 'block !important',
                visibility: 'visible !important',
                opacity: 1,
                position: 'relative',
                zIndex: 20,
                whiteSpace: 'nowrap', // Ensure text doesn't wrap
              }}
            >
              {connectButtonText}
            </Typography>
          </Button>
          <Button
            variant="outlined"
            onClick={handleFollow}
            sx={(theme) => ({
              width: '136px',
              borderColor: isFollowing ? 'black' : theme.palette.secondary.main,
              backgroundColor: 'transparent',
              visibility: 'visible !important',
              display: 'flex !important',
              zIndex: 10,
              '&:hover': {
                backgroundColor: isFollowing
                  ? alpha(theme.palette.secondary.main, 0.12)
                  : 'transparent',
              },
              '&:disabled': {
                visibility: 'visible !important',
                display: 'flex !important',
              },
            })}
          >
            <Typography
              variant={'professionalType' as any}
              color={isFollowing ? 'text.primary' : 'secondary'}
              sx={{
                fontWeight: isFollowing ? 700 : 500,
                fontSize: '16px',
                lineHeight: '24px',
                letterSpacing: '0px',
                display: 'block !important',
                visibility: 'visible !important',
                opacity: 1,
                position: 'relative',
                zIndex: 20,
              }}
            >
              {followButtonText}
            </Typography>
          </Button>
        </Box>
      </CardContent>
    </Card>
  );
}
