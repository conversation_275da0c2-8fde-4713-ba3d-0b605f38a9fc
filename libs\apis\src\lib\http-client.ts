import axios, {
  A<PERSON><PERSON><PERSON><PERSON>r,
  InternalAxiosRequestConfig,
  AxiosRequestConfig,
} from 'axios';
import { auth } from '../lib/firebase/firebase-client.js';
import { signInWithCustomToken } from 'firebase/auth';

const getApiBaseUrl = () => {
  const isBrowser =
    typeof process === 'undefined' ||
    !process.versions ||
    !process.versions.node;

  if (typeof process !== 'undefined' && process.env) {
    if (process.env.NEXT_PUBLIC_SERVER_URL) {
      const baseUrl = process.env.NEXT_PUBLIC_SERVER_URL.endsWith('/')
        ? process.env.NEXT_PUBLIC_SERVER_URL.slice(0, -1)
        : process.env.NEXT_PUBLIC_SERVER_URL;
      return `${baseUrl}/api/v1`;
    } else if (isBrowser) {
      return '/api/v1';
    } else {
      return '/api/v1';
    }
  }

  return '/api/v1';
};

const API_BASE_URL = getApiBaseUrl();

let sessionRefreshAttempts = 0;
const MAX_SESSION_REFRESH_ATTEMPTS = 3;
let useTokenFallbackMode = false;
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value?: any) => void;
  reject: (error?: any) => void;
}> = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });
  
  failedQueue = [];
};

const getCookie = (name: string): string | undefined => {
  if (typeof window === 'undefined' || typeof document === 'undefined')
    return undefined;

  const match = document.cookie.match(
    new RegExp('(^|;\\s*)(' + name + ')=([^;]*)')
  );
  return match ? match.pop() : undefined;
};

// Check if middleware provided session info
const getMiddlewareSessionInfo = () => {
  if (typeof window === 'undefined') return null;
  
  const sessionValid = document.querySelector('meta[name="session-valid"]')?.getAttribute('content') === 'true';
  const userAuthenticated = document.querySelector('meta[name="user-authenticated"]')?.getAttribute('content') === 'true';
  const customToken = document.querySelector('meta[name="custom-token"]')?.getAttribute('content');
  
  return {
    sessionValid,
    userAuthenticated,
    customToken
  };
};

const createAxiosInstance = ({ baseURL }: { baseURL: string }) => {
  const customAxiosInstance = axios.create({
    baseURL,
    headers: {
      'Content-Type': 'application/json',
    },
    withCredentials: true,
  });

  customAxiosInstance.interceptors.request.use(
    async (config: InternalAxiosRequestConfig) => {
      if (!auth) {
        console.log('Auth not initialized in request interceptor');
        return config;
      }

      const currentUser = auth.currentUser;
      const middlewareInfo = getMiddlewareSessionInfo();

      // Get workspace ID from the ID token if available
      let workspaceId: string | null = null;
      if (currentUser) {
        try {
          const idToken = await currentUser.getIdToken();
          const tokenPayload = JSON.parse(atob(idToken.split('.')[1]));
          workspaceId = tokenPayload.workspaceId || null;
          
          // Add workspace ID to headers if available
          if (workspaceId) {
            config.headers['X-Workspace-Id'] = workspaceId;
          }
        } catch (error) {
          console.warn('Failed to extract workspace ID from token:', error);
        }
      }

      // If middleware says session is valid, prefer session cookies
      if (middlewareInfo?.sessionValid && !useTokenFallbackMode) {
        config.withCredentials = true;
        
        // For localhost, explicitly add session cookie if available
        if (
          typeof window !== 'undefined' &&
          window.location.hostname === 'localhost'
        ) {
          const sessionCookie = getCookie('__session');
          if (sessionCookie && !config.headers.Cookie) {
            config.headers.Cookie = `__session=${sessionCookie}`;
            console.log('Added session cookie to request');
          }
        }
        
        // Still add token as backup for certain endpoints
        if (currentUser) {
          try {
            const idToken = await currentUser.getIdToken();
            config.headers.Authorization = `Bearer ${idToken}`;
          } catch (error) {
            console.warn('Could not get ID token, relying on session cookie');
          }
        }
      } else if (currentUser) {
        // Fallback to token-based auth or when middleware indicates session invalid
        try {
          console.log('Getting ID token for request to:', config.url);
          const idToken = await currentUser.getIdToken();
          console.log('Token obtained successfully');

          config.headers.Authorization = `Bearer ${idToken}`;

          if (useTokenFallbackMode) {
            config.withCredentials = false;
            console.log('Using token fallback mode');
          } else {
            config.withCredentials = true;
          }
        } catch (error) {
          console.error('Error getting ID token:', error);
        }
      } else {
        console.log('No current user found in request interceptor');
      }

      return config;
    },
    (error) => Promise.reject(error)
  );

  customAxiosInstance.interceptors.response.use(
    (response) => response,
    async (error: AxiosError) => {
      const config = error.config as any;
      
      // Prevent infinite retry loops
      if (config?._isRetry) {
        return Promise.reject(error);
      }

      // Handle session refresh needed (440 status)
      if (error.response?.status === 440) {
        if (isRefreshing) {
          return new Promise((resolve, reject) => {
            failedQueue.push({ resolve, reject });
          })
            .then(() => {
              if (config) {
                config._isRetry = true;
                return customAxiosInstance(config);
              }
              throw new Error('No config available for retry');
            })
            .catch((err) => Promise.reject(err));
        }

        if (sessionRefreshAttempts >= MAX_SESSION_REFRESH_ATTEMPTS) {
          useTokenFallbackMode = true;
          sessionRefreshAttempts = 0;
          processQueue(new Error('Max refresh attempts reached'));

          if (typeof window !== 'undefined' && auth) {
            await auth.signOut();
            window.location.href = '/signin';
          }

          return Promise.reject({
            message: 'Your session has expired. Please sign in again.',
            code: 'auth/session-expired',
          });
        }

        isRefreshing = true;

        try {
          sessionRefreshAttempts++;

          if (!auth) {
            throw new Error('Firebase auth is not initialized');
          }

          const currentUser = auth.currentUser;
          if (!currentUser) {
            throw new Error('No user signed in');
          }

          const freshIdToken = await currentUser.getIdToken(true);

          await axios.post(
            `${API_BASE_URL}/auth/session-login`,
            { idToken: freshIdToken },
            { withCredentials: true }
          );

          sessionRefreshAttempts = 0;
          processQueue(null, freshIdToken);

          if (config) {
            config._isRetry = true;
            return customAxiosInstance(config);
          }

          return Promise.resolve({} as any);
        } catch (refreshError) {
          useTokenFallbackMode = true;
          processQueue(refreshError);

          return Promise.reject({
            message: 'Your session has expired. Please sign in again.',
            code: 'auth/session-expired',
          });
        } finally {
          isRefreshing = false;
        }
      }

      // Handle unauthorized (401 status)
      if (error.response?.status === 401) {
        if (isRefreshing) {
          return new Promise((resolve, reject) => {
            failedQueue.push({ resolve, reject });
          })
            .then(() => {
              if (config) {
                config._isRetry = true;
                return customAxiosInstance(config);
              }
              throw new Error('No config available for retry');
            })
            .catch((err) => Promise.reject(err));
        }

        if (!auth) {
          if (typeof window !== 'undefined') {
            window.location.href = '/signin';
          }
          return Promise.reject({
            message: 'Firebase auth is not initialized',
            code: 'auth/not-initialized',
          });
        }

        isRefreshing = true;

        try {
          // Try to verify session and get custom token
          const response = await axios.post(
            `${API_BASE_URL}/auth/verify-session`,
            {},
            { withCredentials: true }
          );

          const customToken = response.data?.data?.customToken;

          if (customToken && auth) {
            await signInWithCustomToken(auth, customToken);
            processQueue(null, customToken);

            if (config) {
              config._isRetry = true;
              return customAxiosInstance(config);
            }
          }
        } catch (verifyError) {
          useTokenFallbackMode = true;
          processQueue(verifyError);

          // Let your existing route guards handle the redirect
          console.warn('Session verification failed, route guards will handle redirect');

          return Promise.reject({
            message: 'Authentication failed. Please sign in again.',
            code: 'auth/authentication-failed',
          });
        } finally {
          isRefreshing = false;
        }
      }

      return Promise.reject(error);
    }
  );

  return customAxiosInstance;
};

export const axiosInstance = createAxiosInstance({ baseURL: API_BASE_URL });

export const fetcher = async (args: string | [string, AxiosRequestConfig]) => {
  const [url, config] = Array.isArray(args) ? args : [args, {}];
  const { data } = await axiosInstance.get(url, { ...config });
  return data?.data;
};

/**
 * Reset the token fallback mode and session refresh failure count
 * Call this after a successful login to try session-based auth again
 */
export const resetAuthMode = () => {
  useTokenFallbackMode = false;
  sessionRefreshAttempts = 0;
  isRefreshing = false;
  failedQueue = [];
};

/**
 * Enhanced session refresh with middleware integration
 */
export const refreshSession = async (): Promise<boolean> => {
  if (!auth) {
    throw new Error('Auth is not initialized');
  }

  const currentUser = auth.currentUser;
  
  if (!currentUser) {
    throw new Error('No authenticated user');
  }

  try {
    // Get fresh Firebase ID token
    const idToken = await currentUser.getIdToken(true);
    
    // Call backend to refresh session cookie
    await axiosInstance.post('/auth/session-login', { idToken });
    resetAuthMode();
    return true;
  } catch (error) {
    console.error('Session refresh failed:', error);
    return false;
  }
};