'use client';
import { useEffect, useRef, useState, ReactNode } from 'react';
import { Box, useMediaQuery } from '@mui/material';
import PostHeader from '../PostHeader';
import AddCommentBar from './AddCommentBar';
import OtherPostSidebar from './OtherPostSidebar';
import { FeedPostType, OtherPostProps } from '@minicardiac-client/types';
import CommentsList from '../../comments/CommentsList';
import PostActionsBar from './PostActionsBar';
import { useTheme } from '@emotion/react';
import { useComments } from '@minicardiac-client/apis';

interface PostDetailLayoutProps {
  children: ReactNode;
  user?: {
    name: string;
    profilePic: string;
    postedAgo: string;
  };

  sidebarItems: OtherPostProps[];
  sidebarTitle?: string;
  allowPin?: boolean;
  postId?: string;
  post?: FeedPostType;
}

const PostDetailLayout = ({
  children,
  user = {
    name: '<PERSON>',
    profilePic: '/placeholder-avatar.png',
    postedAgo: 'just now',
  },

  sidebarItems,
  sidebarTitle = 'Other Posts',
  allowPin = false,
  postId,
  post,
}: PostDetailLayoutProps) => {
  const [showAddComment, setShowAddComment] = useState(false);
  const endOfPostRef = useRef<HTMLDivElement | null>(null);

  const theme: any = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  useEffect(() => {
    const currentRef = endOfPostRef.current;
    if (!currentRef) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setShowAddComment(entry.isIntersecting);
      },
      { root: null, threshold: 0.1 }
    );

    observer.observe(currentRef);

    return () => {
      observer.unobserve(currentRef);
      observer.disconnect();
    };
  }, []);

  const { comments: fetchedComments } = useComments(postId || '', {
    enabled: !!postId,
  });

  return (
    <Box
      display="flex"
      flexDirection="column"
      gap={{ xs: '20px', sm: '40px' }}
      width="100%"
      py={{ xs: '16px', sm: '40px' }}
    >
      {/* Header */}
      <Box
        sx={{
          px: { xs: '16px', sm: '40px' },
          maxWidth: '1280px',
          width: '100%',
          mx: 'auto',
        }}
      >
        <PostHeader user={user} showOptions showBackButton />
      </Box>

      <Box
        display="flex"
        justifyContent="center"
        width="100%"
        gap="40px"
        px={{ xs: '16px', sm: '40px' }}
      >
        {/* Main content + comments */}
        <Box
          width={824}
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: '20px',
          }}
        >
          {children}

          <PostActionsBar
            commentsLength={post?.commentsCount || 0}
            likesCount={post?.likesCount || 0}
            repostCount={post?.repostCount || 0}
            shareCount={post?.shareCount || 0}
            isLiked={post?.isLiked || false}
            postId={post?.id || ''}
          />

          {/* Attach the ref to an actual element */}
          {!isMobile && <Box ref={endOfPostRef} sx={{ height: '1px' }} />}

          {(isMobile || showAddComment) && (
            <AddCommentBar postId={postId || ''} />
          )}

          <CommentsList comments={fetchedComments} allowPin={allowPin} />
        </Box>

        {/* Right sidebar */}
        <OtherPostSidebar title={sidebarTitle} articles={sidebarItems} />
      </Box>
    </Box>
  );
};

export default PostDetailLayout;
