import { Dialog, DialogContent, DialogTitle } from '@mui/material';
import { ReactNode } from 'react';

interface CustomDialogProps {
  open: boolean;
  onClose: () => void;
  title?: string;
  children: ReactNode;
  sx?: any;
}

const CustomDialog = ({
  open,
  onClose,
  title,
  children,
  sx,
}: CustomDialogProps) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="lg"
      slotProps={{
        container: {
          sx: {
            alignItems: 'flex-start',
            justifyContent: 'center',
          },
        },
        backdrop: {
          sx: {
            backgroundColor: { xs: 'transparent', sm: '#1E1E1E40' },
          },
        },
      }}
      PaperProps={{
        sx: {
          width: '100%',
          margin: 0,
          boxShadow: 'none',
          height: { xs: '100vh', sm: 'auto' },
        },
      }}
      sx={{
        width: '100%',
        paddingX: '80px',
        paddingTop: '50px',
        alignItems: 'start',
        ...sx,
      }}
    >
      {title && <DialogTitle>{title}</DialogTitle>}
      <DialogContent
        sx={{
          backgroundColor: 'white',
          p: 0,
          '&::-webkit-scrollbar': {
            display: 'none',
          },
          '-ms-overflow-style': 'none',
          'scrollbar-width': 'none',
        }}
      >
        {children}
      </DialogContent>
    </Dialog>
  );
};

export default CustomDialog;
