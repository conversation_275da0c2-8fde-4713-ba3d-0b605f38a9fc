'use client';

import { useState } from 'react';
import { Box, Button, SxProps } from '@mui/material';
import { Icon } from '@iconify/react';
import { useTranslations } from 'next-intl';

import CommentItem from './CommentItem';
import { CommentsListProps } from '@minicardiac-client/types';

const COMMENTS_PER_PAGE = 5;

const CommentsList = ({
  comments,
  allowPin = false,
  postId,
  sx,
}: CommentsListProps & {
  allowPin?: boolean;
  postId?: string;
  sx?: SxProps;
}) => {
  const [visibleCount, setVisibleCount] = useState(COMMENTS_PER_PAGE);
  const visibleComments = comments.slice(0, visibleCount);

  const t = useTranslations('comment');

  const handleShowMore = () =>
    setVisibleCount((prev) => prev + COMMENTS_PER_PAGE);

  return (
    <Box
      display="flex"
      flexDirection="column"
      gap="8px"
      pr="8px"
      mb="86px"
      sx={{ ...sx }}
    >
      {visibleComments.map((comment) => (
        <CommentItem
          key={comment.id}
          comment={comment}
          allowPin={allowPin}
          postId={postId}
        />
      ))}
      {visibleCount < comments.length && (
        <Button onClick={handleShowMore} sx={{ color: '#A24295' }}>
          {t('showMore')}
          <Icon icon="mdi:chevron-down" width={20} height={20} />
        </Button>
      )}
    </Box>
  );
};

export default CommentsList;
