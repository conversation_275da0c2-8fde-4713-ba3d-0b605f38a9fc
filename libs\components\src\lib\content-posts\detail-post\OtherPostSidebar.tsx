import { Box, Typography } from '@mui/material';
import ArticleCard from './ArticleCard';
import { OtherPostProps } from '@minicardiac-client/types';

const OtherPostSidebar = ({
  title,
  articles,
}: {
  title: string;
  articles: OtherPostProps[];
}) => (
  <Box
    width={{ xs: '270px', lg: '336px' }}
    height="fit-content"
    maxHeight={660}
    sx={{
      overflowY: 'scroll',
      p: '20px',
      '&::-webkit-scrollbar': { display: 'none' },
      boxShadow:
        '0px 12px 24px rgba(0, 0, 0, 0.05), 0px 0px 2px rgba(0,0,0,0.1)',
      borderRadius: '8px',
      display: { xs: 'none', md: 'block' },
    }}
  >
    <Typography fontSize="20px" fontWeight={600} mb="40px">
      {title}
    </Typography>

    <Box display="flex" flexDirection="column" gap="40px">
      {articles.map((article, index) => (
        <ArticleCard 
          key={article.postId || `article-${index}`} 
          article={article} 
        />
      ))}
    </Box>
  </Box>
);

export default OtherPostSidebar;
