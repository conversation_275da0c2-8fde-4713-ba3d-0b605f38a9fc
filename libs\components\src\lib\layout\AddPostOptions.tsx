import { Box, Divider, Typography } from '@mui/material';
import { useTheme } from '@emotion/react';

import TextIcon from '../Icons/PostIcons/TextIcon';
import MediaIcon from '../Icons/PostIcons/MediaIcon';
import ArticleIcon from '../Icons/PostIcons/ArticleIcon';
import PollIcon from '../Icons/PostIcons/PollIcon';
import QuestionIcon from '../Icons/PostIcons/QuestionIcon';
import ScheduledIcon from '../Icons/PostIcons/ScheduledIcon';
import DraftIcon from '../Icons/PostIcons/DraftIcon';

import { usePostDialogStore } from '@minicardiac-client/apis';

// Icon and label configuration
const iconOptions = [
  { icon: <TextIcon />, label: 'Text' },
  { icon: <MediaIcon />, label: 'Media' },
  { icon: <ArticleIcon />, label: 'Article' },
  { icon: <PollIcon />, label: 'Poll' },
  { icon: <QuestionIcon />, label: 'Question' },
];

const statusOptions = [
  { icon: <ScheduledIcon />, label: 'Scheduled' },
  { icon: <DraftIcon />, label: 'Drafts' },
];

const AddPostOptions = () => {
  const theme: any = useTheme();

  const { setActiveDialog } = usePostDialogStore();

  const handleIconClick = (label: string) => {
    setActiveDialog(label);
  };

  return (
    <Box
      display="flex"
      justifyContent="center"
      py="20px"
      alignItems="center"
      width="100%"
      px="20px"
    >
      {/* Left Icons */}
      <Box
        display="flex"
        alignItems="center"
        justifyContent="space-between"
        gap="40px"
        width="65%"
        mr="33px"
      >
        {iconOptions.map(({ icon, label }, index) => (
          <Box
            key={index}
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            borderRadius="8px"
            px="8px"
            gap="8px"
            onClick={() => handleIconClick(label)}
            sx={{
              cursor: 'pointer',
              transition: 'all 0.1s ease',
              width: '52px',
              padding: '8px',
              '&:hover': {
                width: { md: '82px', lg: '82px' },
                boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                scale: '1.1',
              },
            }}
          >
            <Box sx={{ width: '36px', height: '36px' }}>{icon}</Box>
            <Typography sx={{ fontSize: '14px', fontWeight: 500 }}>
              {label}
            </Typography>
          </Box>
        ))}
      </Box>

      <Divider
        orientation="vertical"
        flexItem
        sx={{
          backgroundColor: theme.palette.neutral.main,
          width: '1px',
          height: '62px',
          flexShrink: 0,
        }}
      />

      {/* Right Status Icons */}
      <Box
        display="flex"
        alignItems="center"
        justifyContent={{ xl: 'center' }}
        gap={{ smd: '70px', lg: '50px' }}
        width="25%"
        ml="34px"
      >
        {statusOptions.map(({ icon, label }, index) => (
          <Box
            key={index}
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            borderRadius="8px"
            px="8px"
            gap="8px"
            onClick={() => handleIconClick(label)}
            sx={{
              cursor: 'pointer',
              transition: 'all 0.1s ease',
              width: '52px',
              padding: '8px',
              '&:hover': {
                width: { md: '82px', lg: '82px' },
                boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                scale: '1.1',
              },
            }}
          >
            <Box sx={{ width: '36px', height: '36px' }}>{icon}</Box>
            <Typography sx={{ fontSize: '14px', fontWeight: 500 }}>
              {label}
            </Typography>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default AddPostOptions;
