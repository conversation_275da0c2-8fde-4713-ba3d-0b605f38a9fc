'use client';

import { Box, useMediaQuery } from '@mui/material';
import { AuthLayoutProps } from '../types/auth.types';
import { SLIDES } from '../constants/auth.constants';
import Carousel from '../../../../../components/src/lib/carousel/Carousel';
import { BackButton } from './BackButton';
import GradientOverlayBox from './GradientOverlayBox';
import { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';
import { useTheme } from '@emotion/react';

export const AuthLayout = ({
  children,
  showCarousel = true,
  showBackButton = false,
  onBackClick,
  sx = {},
  activeTab,
}: AuthLayoutProps) => {
  const theme: any = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const pathname = usePathname();
  const [marginTop, setMarginTop] = useState('150px');

  useEffect(() => {
    if (activeTab === 0 && isMobile) {
      setMarginTop('150px');
    } else {
      setMarginTop('0px');
    }
  }, [activeTab, isMobile]);

  return (
    <Box
      sx={{
        display: 'flex',
        height: '100vh',
        minHeight: '100vh',
        overflow: 'hidden',
        justifyContent: 'center',
        backgroundColor: '#F3F4F6',
      }}
    >
      <Box
        sx={{
          width: '100%',
          height: '100%',
          display: 'flex',
          maxHeight: '1000px',
          maxWidth: '1500px',
          justifyContent: 'center',
          margin: 'auto',
        }}
      >
        <Box
          sx={{
            width: {
              xs: '100%',
              sm: '100%',
              md: showCarousel ? '50%' : '100%',
            },
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'flex-start',
            position: 'relative',
            overflowY: 'auto',
            overflowX: 'hidden',
            scrollbarWidth: 'none',
            msOverflowStyle: 'none',
            '&::-webkit-scrollbar': {
              display: 'none',
            },
            ...sx,
          }}
        >
          {showBackButton && <BackButton handleBackButton={onBackClick} />}

          <Box
            sx={{
              position: 'relative',
              width: '100%',
              minHeight: '100%',
              display: 'flex',
              justifyContent: 'center',
            }}
          >
            <GradientOverlayBox fadeOut={false} />

            <Box
              sx={{
                width: '100%',
                height: '100%',
                maxHeight: '1000px',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                pt: { lg: '0px' },
                mt: isMobile ? marginTop : '0px',
                borderRadius:
                  activeTab !== 0 || !isMobile ? '0px' : '40px 40px 0 0',
                position: 'relative',
                zIndex: 2,
                backgroundColor: 'white',
                transition:
                  (pathname && !pathname.includes('/signup')) || activeTab === 0
                    ? 'margin-top 0.5s ease, border-radius 0.5s ease'
                    : 'none',
              }}
            >
              {children}
            </Box>
          </Box>
        </Box>

        {showCarousel && (
          <Box
            sx={{
              width: '50%',
              minHeight: '100%',
              display: {
                xs: 'none',
                sm: 'none',
                md: 'block',
              },
            }}
          >
            <Carousel slides={SLIDES} />
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default AuthLayout;
