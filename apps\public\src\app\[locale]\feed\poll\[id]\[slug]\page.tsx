'use client';

import { useParams } from 'next/navigation';

import { FullPageLoader } from '@minicardiac-client/components';
import { useAuth } from '@minicardiac-client/apis';

import PostDetailLayout from '@/libs/components/src/lib/content-posts/detail-post/PostDetailLayout';
import { otherArticles } from '@/libs/components/src/lib/article-post/mockData';
import { PollCaption } from '@/libs/components/src/lib/poll-post/PollCaption';
import MainPollContent from '@/libs/components/src/lib/poll-post/MainPollContent';
// import Head from '@/libs/components/src/lib/head/Head';

export const POLL_CAPTION = `With the growing body of evidence supporting the use of SGLT2 inhibitors in HFpEF patients, are you now routinely prescribing them as part of your initial management plan?`;

export default function PollPostPageWrapper() {
  const { authState } = useAuth();
  const params = useParams();

  if (authState.isLoading) {
    return <FullPageLoader open={true} />;
  }

  const postId = params?.id as string;

  return (
    <>
      {/* // Doing this after api integration */}
      {/* <Head title={post.title ?? post.content} /> */}

      <PostDetailLayout
        user={{
          name: 'Roger Taylor',
          profilePic: '/placeholder-avatar.png',
          postedAgo: 'just now',
        }}
        sidebarItems={otherArticles}
        sidebarTitle="Other Posts"
      >
        <PollCaption POLL_CAPTION={POLL_CAPTION} postId={postId} />
        <MainPollContent />
      </PostDetailLayout>
    </>
  );
}
