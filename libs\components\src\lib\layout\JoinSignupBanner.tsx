'use client';

import { useState } from 'react';
import { Box, IconButton, useMediaQuery, Button } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useRouter } from 'next/navigation';
import { useTheme } from '@emotion/react';

export default function JoinSignupBanner() {
  const [visible, setVisible] = useState(true);
  const router = useRouter();
  const theme: any = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  if (!visible) return null;

  return (
    <Box
      sx={{
        position: 'fixed',
        bottom: 0,
        left: isMobile ? 0 : '50%',
        transform: isMobile ? 'none' : 'translateX(-50%)',
        zIndex: 999,
        backgroundColor: 'white',
        border: '2px solid #A24295',
        borderBottom: 'none',
        borderTopLeftRadius: '16px',
        borderTopRightRadius: '16px',
        width: '100%',
        maxWidth: 800,
        padding: '16px 24px',
        boxShadow: '0px -2px 8px rgba(0, 0, 0, 0.06)',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'column',
        textAlign: 'center',
        gap: 2,
      }}
    >
      {/* Close Button */}
      <IconButton
        aria-label="close"
        onClick={() => setVisible(false)}
        sx={{
          position: 'absolute',
          top: 8,
          right: 8,
          color: '#A24295',
        }}
      >
        <CloseIcon />
      </IconButton>

      {/* Content */}
      <Box>
        <Box sx={{ fontWeight: 600, fontSize: '28px', mb: 0.5 }}>
          Join the community!
        </Box>
        <Box sx={{ fontSize: '16px', color: '#666' }}>
          Are you a part of the cardiac world? Join the community today to
          establish your professional presence, build your network, find jobs
          and opportunities, and collaborate with colleagues!
        </Box>
      </Box>

      {/* CTA Button */}
      <Box sx={{ mt: { xs: 1, sm: 0 }, flexShrink: 0 }}>
        <Button
          onClick={() => router.push('/signin')}
          sx={{
            backgroundColor: '#A24295',
            color: 'white',
            padding: '8px 16px',
            borderRadius: '8px',
            fontWeight: 600,
            textDecoration: 'none',
            '&:hover': {
              backgroundColor: '#902b83',
              color: 'white',
              textDecoration: 'none',
            },
          }}
        >
          Sign Up!
        </Button>
      </Box>
    </Box>
  );
}
