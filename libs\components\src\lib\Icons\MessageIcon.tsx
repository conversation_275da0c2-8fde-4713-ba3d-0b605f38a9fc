import { useState } from 'react';

const MessageIcon = ({
  fill = '#A3A3A3',
  hoverFill = '#A24295',
  size = 24,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const currentFill = isHovered ? hoverFill : fill;

  return (
    <svg
      width={size}
      height={(size * 23) / 24}
      viewBox="0 0 24 23"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <path
        d="M12.7955 22.5C10.4216 22.5 8.20718 21.777 6.28665 20.4105C5.40607 20.8657 4.07218 21.4286 2.92472 21.4286C1.08438 21.4286 0.363228 20.7862 0.229733 20.6522C0.0431969 20.4649 -0.0370443 20.2232 0.016103 19.9826C0.0692504 19.7419 0.202639 19.5002 0.416269 19.3935C1.08321 19.019 2.73702 17.8397 2.92355 16.5537C2.04401 14.9194 1.5907 13.0979 1.5907 11.2499C1.5907 5.03595 6.60638 0 12.7954 0C18.9843 0 24 5.03595 24 11.2499C24 17.4639 18.9844 22.5 12.7955 22.5ZM12.7955 9.9108C12.0483 9.9108 11.4616 10.4999 11.4616 11.2501C11.4616 12.0003 12.0483 12.5894 12.7955 12.5894C13.5427 12.5894 14.1294 12.0003 14.1294 11.2501C14.1294 10.4999 13.5427 9.9108 12.7955 9.9108ZM7.99349 9.9108C7.2463 9.9108 6.6596 10.4999 6.6596 11.2501C6.6596 12.0003 7.2463 12.5894 7.99349 12.5894C8.74068 12.5894 9.32737 12.0003 9.32737 11.2501C9.32737 10.4999 8.74068 9.9108 7.99349 9.9108ZM17.5975 9.9108C16.8503 9.9108 16.2636 10.4999 16.2636 11.2501C16.2636 12.0003 16.8503 12.5894 17.5975 12.5894C18.3447 12.5894 18.9314 12.0003 18.9314 11.2501C18.9314 10.4999 18.3447 9.9108 17.5975 9.9108Z"
        fill={currentFill}
      />
    </svg>
  );
};

export default MessageIcon;
