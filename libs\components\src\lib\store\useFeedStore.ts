import { create } from 'zustand';
import { FeedState } from '@minicardiac-client/types';

export const useFeedStore = create<FeedState>((set) => ({
  feed: [],
  setFeed: (feed) => set({ feed }),
  updateLike: (postId, isLiked) =>
    set((state) => ({
      feed: state.feed.map((post) =>
        post.id === postId
          ? {
              ...post,
              isLiked,
              likesCount: Math.max(0, post.likesCount + (isLiked ? 1 : -1)),
            }
          : post
      ),
    })),
}));
