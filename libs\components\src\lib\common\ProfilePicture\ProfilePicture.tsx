import { Box, Typography, IconButton, Tooltip, CircularProgress } from '@mui/material';
import Image from 'next/image';
import { useRef } from 'react';
import { ExtendedTheme } from '../../auth';
import Iconify from '../../iconify/iconify';
import { getInitials } from '@minicardiac-client/utilities';
import { BLANK_PROFILE } from '../../auth/constants/auth.constants';

export interface ProfilePictureProps {
  theme: ExtendedTheme;
  previewUrl: string | null;
  displayName: string;
  onImageChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onRemove: () => void;
  isUploading?: boolean;
  error?: string | null;
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  changePhotoLabel?: string;
  addProfilePicture?: string;
  removePhotoLabel?: string;
}

export const ProfilePicture = ({
  theme,
  previewUrl,
  displayName,
  onImageChange,
  onRemove,
  isUploading = false,
  error = null,
  size = 'medium',
  disabled = false,
  changePhotoLabel = 'Change photo',
  addProfilePicture = '+Add profile picture', 
  removePhotoLabel = 'Remove photo',
}: ProfilePictureProps) => {
  const inputRef = useRef<HTMLInputElement>(null);

  // Size variants
  const sizeMap = {
    small: { container: 120, icon: 24, fontSize: '32px' },
    medium: { container: 160, icon: 36, fontSize: '48px' },
    large: { container: 200, icon: 48, fontSize: '64px' },
  };

  const { container, icon, fontSize } = sizeMap[size];

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
      {/* Image Container */}
      <Box
        sx={{
          width: container,
          height: container,
          borderRadius: '50%',
          backgroundColor: '#A2429514',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Background pattern */}
        <Box
          component="img"
          src={BLANK_PROFILE}
          alt="Profile background"
          sx={{
            position: 'absolute',
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            scale: 1.1,
            backgroundSize: '8px 8px',
            backgroundRepeat: 'repeat',
            backgroundPosition: 'center',
          }}
        />

        {/* Preview or Initials */}
        {previewUrl ? (
          <Image
            src={previewUrl}
            alt="Profile"
            fill
            style={{ objectFit: 'cover' }}
            unoptimized={previewUrl.startsWith('blob:')}
          />
        ) : (
          <Typography
            sx={{
              fontSize,
              fontWeight: 500,
              color: (theme.palette as any).neutral?.[900],
              position: 'relative',
            }}
          >
            {getInitials(displayName)}
          </Typography>
        )}

        {/* Loading Overlay */}
        {isUploading && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: 'rgba(0,0,0,0.5)',
            }}
          >
            <CircularProgress size={icon} />
          </Box>
        )}
      </Box>

      {/* Action Buttons */}
      {previewUrl ? (
        <Box sx={{ display: 'flex', gap: 2, mt: 2.5 }}>
          <Tooltip title={changePhotoLabel}>
            <IconButton
              onClick={() => inputRef.current?.click()}
              disabled={disabled || isUploading}
              size="small"
            >
              <Iconify
                icon="solar:pen-bold"
                width={24}
                color={theme.palette.secondary.main}
              />
            </IconButton>
          </Tooltip>
          <Tooltip title={removePhotoLabel}>
            <IconButton
              onClick={onRemove}
              disabled={disabled || isUploading}
              size="small"
            >
              <Iconify
                icon="solar:trash-bin-trash-bold"
                width={24}
                color={theme.palette.secondary.main}
              />
            </IconButton>
          </Tooltip>
        </Box>
      ) : (
        <Typography
          onClick={() => inputRef.current?.click()}
          sx={{
            mt: 3,
            color: theme.palette.secondary.main,
            fontWeight: 600,
            cursor: 'pointer',
            '&:hover': {
              textDecoration: 'underline',
            },
            textAlign: 'center',
            width: '100%'
          }}
        >
          {addProfilePicture}
        </Typography>
      )}

      {/* Error Message */}
      {error && (
        <Typography color="error" variant="caption" sx={{ mt: 1 }}>
          {error}
        </Typography>
      )}

      {/* Hidden file input */}
      <input
        ref={inputRef}
        type="file"
        accept="image/*"
        onChange={onImageChange}
        style={{ display: 'none' }}
        disabled={disabled || isUploading}
      />
    </Box>
  );
}; 