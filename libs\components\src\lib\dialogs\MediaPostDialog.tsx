'use client';

import { useState } from 'react';
import {
  <PERSON>,
  Typography,
  Menu,
  MenuItem,
  Stack,
  TextField,
  useMediaQuery,
} from '@mui/material';
import { toast } from 'react-toastify';
import { Iconify } from '../iconify';
import { PostButton } from '../buttons/PostButton';
import { LoadingButton } from '../loading-button';
import CustomDialog from './CustomDialog';
import { isContentEmpty } from '@minicardiac-client/utilities';

import { CustomizedSteppers } from '../onboarding';
import MediaPostUpload from '../media-post/MediaPostUpload';
import MediaPostCaption from './MediaPostCaption';
import CustomToggleButtonGroup from '../buttons/CustomToggleButtonGroup';
import { BackButton } from '../buttons/Backbutton';
import { useTheme } from '@mui/material/styles';
import { useTranslations } from 'next-intl';
import { useCreateMediaPost } from '@minicardiac-client/apis';
import type { CreateMediaPostRequest } from '@minicardiac-client/apis';
import SchedulePostDialog from './ScheduleDialog';

interface MediaPostDialogProps {
  open: boolean;
  onClose: () => void;
  setOpenScheduleDialog: () => void;
  uploadedFiles: File[];
  setUploadedFiles: (files: File[]) => void;
}

const MediaPostDialog = ({
  open,
  onClose,
  setOpenScheduleDialog,
  uploadedFiles,
  setUploadedFiles,
}: MediaPostDialogProps) => {
  const [caption, setCaption] = useState('');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [activeStep, setActiveStep] = useState(0);

  const [tags, setTags] = useState('');
  const [audience, setAudience] = useState('PROFESSIONAL');
  const [speciality, setSpeciality] = useState('CARDIAC_SURGERY');
  const [postScheduleDate, setPostScheduleDate] = useState<string | undefined>(undefined);
  const [scheduleDialogOpen, setScheduleDialogOpen] = useState(false);

  const theme = useTheme();
  const screenBelowSM = useMediaQuery(theme.breakpoints.down('sm'));

  const openMenu = Boolean(anchorEl);
  const t = useTranslations('mediaPost');

  const handleFilesChange = (files: File[]) => {
    setUploadedFiles(files);
  };

  const handleNextStep = () => {
    if (uploadedFiles.length === 0) return;
    setActiveStep((prev) => prev + 1);
  };

  const createMediaPost = useCreateMediaPost({
    onSuccess: () => {
      toast.success('Media post created successfully!');
      onClose();
    },
    onError: (err) => {
      toast.error(err.message || 'Failed to create media post');
    },
  });

  const audienceMap: Record<string, string> = {
    CARDIAC_SURGERY: 'CARDIAC_SURGEON',
    CARDIOLOGY: 'CARDIOLOGIST',
    BOTH: 'BOTH',
  };

  const handleSchedule = async (isoDate: string) => {
    setPostScheduleDate(isoDate);
    setScheduleDialogOpen(false);
    await handlePostWithSchedule(isoDate);
  };

  const handlePostWithSchedule = async (isoDate: string) => {
    if (!uploadedFiles.length) return;
    const payload: Omit<CreateMediaPostRequest, 'medias'> & { files: File[] } = {
      files: uploadedFiles,
      tags: Array.isArray(tags)
        ? tags
        : tags.split(/[\,\s]+/).filter(Boolean),
      community: audience as CreateMediaPostRequest['community'],
      audience: audienceMap[speciality] as CreateMediaPostRequest['audience'],
      postStatus: 'scheduled' as const,
      postScheduleDate: isoDate,
      content: caption,
    };
    createMediaPost.mutate(payload);
  };

  const handlePost = () => {
    if (!uploadedFiles.length) return;
    const postStatus: 'draft' | 'published' | 'scheduled' = postScheduleDate ? 'scheduled' : 'published';
    const payload: Omit<CreateMediaPostRequest, 'medias'> & { files: File[] } =
      {
        files: uploadedFiles,
        tags: Array.isArray(tags)
          ? tags
          : tags.split(/[\,\s]+/).filter(Boolean),
        community: audience as CreateMediaPostRequest['community'],
        audience: audienceMap[speciality] as CreateMediaPostRequest['audience'],
        postStatus,
        postScheduleDate,
        content: caption,
      };
    createMediaPost.mutate(payload);
  };

  return (
    <CustomDialog
      open={open}
      onClose={onClose}
      title=""
      sx={{
        p: 0,
        px: { xs: 0, sm: '80px' },
        pt: { xs: 0, sm: '50px' },
        alignItems: { xs: 'stretch', sm: 'start' },
        '.MuiDialog-paper': {
          maxHeight: { xs: '100%', sm: 'calc(100% - 64px)' },
        },
      }}
    >
      <Box
        display="flex"
        flexDirection="column"
        padding={{ xs: '16px', sm: '40px' }}
        width="100%"
        sx={{ backgroundColor: 'white' }}
      >
        {/* Heading */}
        <Box
          position="relative"
          display="flex"
          justifyContent={{ xs: 'space-between', sm: 'center' }}
          alignItems="center"
          height="35px"
          mb={'20px'}
        >
          <Box sx={{ display: 'flex', gap: '16px', alignItems: 'center' }}>
            {screenBelowSM && <BackButton onClick={onClose} />}
            <Typography
              sx={{
                fontFamily: 'Plus Jakarta Sans',
                fontWeight: 500,
                fontSize: { xs: '20px', sm: '28px' },
                color: '#1E1E1E',
              }}
            >
              {t('newMediaPost')}
            </Typography>
          </Box>
          <Typography
            sx={{
              position: 'absolute',
              right: 0,
              top: 0,
              fontFamily: 'Plus Jakarta Sans',
              fontWeight: 600,
              fontSize: '16px',
              color: '#A24295',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
            }}
          >
            {t('drafts')} <Iconify icon="solar:arrow-right-linear" />
          </Typography>
        </Box>

        {/* Stepper */}
        <CustomizedSteppers
          activeStep={activeStep}
          steps={[t('addMedia'), t('addDetails')]}
        />

        {/* Step content */}
        <Box sx={{ width: '100%', mt: '20px' }}>
          {activeStep === 0 ? (
            <MediaPostUpload onFilesChange={handleFilesChange} />
          ) : (
            <MediaPostCaption
              images={uploadedFiles}
              caption={caption}
              setCaption={setCaption}
            />
          )}
        </Box>

        {/* Tags and Toggles */}
        {activeStep === 1 && (
          <Box
            display="flex"
            flexDirection={{ xs: 'column', md: 'row', lg: 'row' }}
            gap={{ xs: '10px', lg: '40px' }}
            alignItems={{ xs: 'center', md: 'end' }}
            mt={'20px'}
            mb={{ xs: '140px', sm: '0px' }}
          >
            <TextField
              placeholder={t('tagsPlaceholder')}
              value={tags}
              onChange={(e) => setTags(e.target.value)}
              fullWidth
              label={t('tags')}
              InputLabelProps={{ shrink: true }}
            />
            <Box width={{ xs: '100%', md: '224px' }}>
              <CustomToggleButtonGroup
                label={t('community')}
                options={['PROFESSIONAL', 'PUBLIC', 'BOTH']}
                selected={audience}
                onChange={setAudience}
                width={{ xs: '100%', md: '224px' }}
              />
            </Box>
            <Box width={{ xs: '100%', md: '282px' }}>
              <CustomToggleButtonGroup
                label={t('audience')}
                options={['CARDIAC_SURGERY', 'CARDIOLOGY', 'BOTH']}
                selected={speciality}
                onChange={setSpeciality}
                width={{ xs: '100%', md: '282px' }}
              />
            </Box>
          </Box>
        )}

        {/* Footer Buttons */}
        <Stack
          direction={{ xs: 'column', sm: 'row' }}
          spacing={{ xs: '10px', sm: '20px' }}
          justifyContent={{ xs: 'center', sm: 'center' }}
          sx={{
            position: { xs: 'fixed', sm: 'static' },
            bottom: 0,
            left: 0,
            mt: { xs: '0px', sm: '40px' },
            width: { xs: '100%', sm: 'auto' },
            background: { xs: 'white', sm: 'transparent' },
            padding: { xs: '20px', sm: 0 },
            boxShadow: { xs: '0 -4px 20px 0 rgba(0,0,0,0.1)', sm: 'none' },
          }}
        >
          <Box
            sx={{
              display: 'flex',
              width: '100%',
              justifyContent: 'center',
              alignItems: 'center',
              flexDirection: { xs: 'column', sm: 'row' },
              gap: '20px',
            }}
          >
            <LoadingButton
              variant="outlined"
              onClick={onClose}
              sx={{
                width: { xs: '156px' },
                height: '40px',
                backgroundColor: 'white',
                border: { xs: 'none', sm: '1px solid #A24295' },
                color: '#A24295',
                '&:hover': {
                  backgroundColor: 'secondary.light',
                },
                fontSize: '16px',
                fontWeight: 700,
              }}
            >
              {t('cancel')}
            </LoadingButton>

            {activeStep === 0 ? (
              <LoadingButton
                onClick={handleNextStep}
                variant="contained"
                disabled={uploadedFiles.length === 0}
                sx={{
                  width: { xs: '156px' },
                  height: '40px',
                  backgroundColor:
                    uploadedFiles.length === 0 ? '#ccc' : '#A24295',
                  color: 'white',
                  fontSize: '16px',
                  fontWeight: 700,
                  '&:hover': {
                    backgroundColor:
                      uploadedFiles.length === 0 ? '#ccc' : '#8d2a7b',
                  },
                }}
              >
                {t('next')}
              </LoadingButton>
            ) : (
              <>
                <PostButton
                  setAnchorEl={setAnchorEl}
                  handlePost={handlePost}
                  disabled={
                    isContentEmpty(caption) || createMediaPost.isPending
                  }
                  isOpen={openMenu}
                />
                <Menu
                  anchorEl={anchorEl}
                  open={openMenu}
                  onClose={() => setAnchorEl(null)}
                >
                  <MenuItem onClick={() => setScheduleDialogOpen(true)}>
                    {t('schedule')}
                  </MenuItem>
                  <MenuItem onClick={() => setAnchorEl(null)}>
                    {t('saveDraft')}
                  </MenuItem>
                  <MenuItem onClick={() => setAnchorEl(null)}>
                    {t('addToSponsorship')}
                  </MenuItem>
                </Menu>
              </>
            )}
          </Box>
        </Stack>
      </Box>
      <SchedulePostDialog
        open={scheduleDialogOpen}
        onClose={() => setScheduleDialogOpen(false)}
        onSchedule={handleSchedule}
      />
    </CustomDialog>
  );
};

export default MediaPostDialog;
