import { DraggableAttributes, useDraggable } from '@dnd-kit/core';
import { SyntheticListenerMap } from '@dnd-kit/core/dist/hooks/utilities';
import { ReactNode } from 'react';

type DraggablePostProps = {
  postId: string;
  type: string;
  disabled?: boolean;
  children: (props: {
    setNodeRef: (element: HTMLElement | null) => void;
    listeners: SyntheticListenerMap | undefined;
    attributes: DraggableAttributes;
  }) => ReactNode;
};

export default function DraggablePost({
  postId,
  type,
  disabled,
  children,
}: DraggablePostProps) {
  const { attributes, listeners, setNodeRef } = useDraggable({
    id: `${postId}--${type}`,
    disabled,
  });

  return <>{children({ setNodeRef, listeners, attributes })}</>;
}
