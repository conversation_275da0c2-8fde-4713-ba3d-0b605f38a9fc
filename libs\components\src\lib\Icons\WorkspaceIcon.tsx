import { useState } from 'react';

const WorkspaceIcon = ({
  fill = '#A3A3A3',
  hoverFill = '#A24295',
  size = 24,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const currentFill = isHovered ? hoverFill : fill;

  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <path
        d="M15.6039 0C14.1745 0 13.0156 1.15869 13.0156 2.58803V9.57964C13.0156 11.009 14.1745 12.1677 15.6039 12.1677H21.4079C22.8374 12.1677 23.9962 11.0089 23.9962 9.57964V2.58803C23.9962 1.15869 22.8374 0 21.4079 0H15.6039Z"
        fill={currentFill}
      />
      <path
        d="M2.58827 0C1.15884 0 0 1.15869 0 2.58803V5.31864C0 6.74798 1.15884 7.90667 2.58827 7.90667H8.39226C9.82173 7.90667 10.9805 6.74798 10.9805 5.31864V2.58803C10.9805 1.15869 9.82173 0 8.39226 0H2.58827Z"
        fill={currentFill}
      />
      <path
        d="M2.58827 10.1289C1.15884 10.1289 0 11.2876 0 12.7169V21.4124C0 22.8417 1.15884 24.0004 2.58827 24.0004H8.39226C9.82173 24.0004 10.9805 22.8417 10.9805 21.4124V12.7169C10.9805 11.2876 9.82173 10.1289 8.39226 10.1289H2.58827Z"
        fill={currentFill}
      />
      <path
        d="M15.6039 14.1582C14.1745 14.1582 13.0156 15.3169 13.0156 16.7462V21.4118C13.0156 22.8412 14.1745 23.9999 15.6039 23.9999H21.4079C22.8374 23.9999 23.9962 22.8412 23.9962 21.4118V16.7462C23.9962 15.3169 22.8374 14.1582 21.4079 14.1582H15.6039Z"
        fill={currentFill}
      />
    </svg>
  );
};

export default WorkspaceIcon;
