import { getCdnUrl } from '@minicardiac-client/utilities';
import { Box, Typography } from '@mui/material';

const ArticleCover = ({
  thumbnail,
  articleTitle,
}: {
  thumbnail: string;
  articleTitle: string;
}) => (
  <Box
    sx={{
      display: 'flex',
      flexDirection: 'column',
    }}
  >
    <Box
      component="img"
      src={getCdnUrl(thumbnail)}
      alt="cover"
      sx={{
        maxWidth: '824px',
        height: { xs: '196px', sm: '385px' },
        borderRadius: '8px',
        objectFit: 'cover',
      }}
    />
    <Typography
      fontSize="24px"
      fontWeight={600}
      mt={{ xs: '20px', sm: '16px' }}
      mb={'16px'}
    >
      {articleTitle}
    </Typography>
  </Box>
);

export default ArticleCover;
