'use client';

import { Box } from '@mui/material';
import { useState, useEffect, useCallback } from 'react';
import { usePathname } from 'next/navigation';
import SignInForm from './SignInForm';
import SignupTypeList from './SignupTypeList';
import SignUpForm from './SignUpForm';
import { AnimatePresence, motion } from 'framer-motion';

interface AuthTabContentProps {
  onSubmit?: (data: {
    email: string;
    password: string;
    displayName?: string;
    organizationName?: string;
  }) => void;
  onForgotPassword?: () => void;
  onTypeSelect?: (path: string) => void;
  activeTab: number;
  isLoading?: boolean;
  error?: string | null;
  displayNameLabel?: string;
  namePlaceholder?: string;
  emailLabel?: string;
  passwordLabel?: string;
  forgotPasswordLabel?: string;
  continueLabel?: string;
  orLabel?: string;
  googleLabel?: string;
  appleLabel?: string;
  locale?: string;
}

const AuthTabContent = (props: AuthTabContentProps) => {
  const {
    onSubmit,
    onForgotPassword,
    onTypeSelect,
    activeTab = 0,
    isLoading,
    error,
    displayNameLabel = 'Display Name',
    namePlaceholder = 'Name',
    emailLabel = 'Email',
    passwordLabel = 'Password',
    forgotPasswordLabel = 'Forgot Password?',
    continueLabel = 'Continue',
    orLabel = 'OR',
    googleLabel = 'Continue with Google',
    appleLabel = 'Continue with Apple',
  } = props;

  const pathname = usePathname();
  const [userType, setUserType] = useState<string>('');
  const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>({
    'signin-password': false,
    'signup-password': false,
    'confirm-password': false,
  });

  const togglePasswordVisibility = useCallback(
    (fieldId: 'signin-password' | 'signup-password' | 'confirm-password') => {
      setShowPasswords((prev) => ({
        ...prev,
        [fieldId]: !prev[fieldId],
      }));
    },
    []
  );

  useEffect(() => {
    if (!pathname) return;
    const pathParts = pathname.split('/').filter(Boolean);
    const signupIndex = pathParts.indexOf('signup');
    if (signupIndex !== -1 && pathParts.length > signupIndex + 1) {
      const typeFromUrl = pathParts[signupIndex + 1];
      setUserType(typeFromUrl);
    } else {
      setUserType('');
    }
  }, [pathname, activeTab]);

  const handleTypeSelect = (path: string) => {
    if (onTypeSelect) onTypeSelect(path);
  };

  const motionBoxStyle = {
    position: 'absolute' as const,
    width: '100%',
    top: 0,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: '32px',
    display: 'flex',
    flexDirection: 'column' as const,
    alignItems: 'center',
    justifyContent: 'center',
  };

  const slideVariants = {
    enter: (dir: number) => {
      return {
        x: activeTab ? '100%' : '-100%',
        opacity: 0,
        position: 'absolute' as const,
      };
    },
    center: {
      x: 0,
      opacity: 1,
      position: 'relative' as const,
    },
    exit: (dir: number) => {
      return {
        x: activeTab === 1 ? '100%' : '-100%',
        opacity: 0,
        position: 'absolute' as const,
      };
    },
  };

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
      }}
    >
      <Box
        sx={{
          width: {
            md: '600px',
            sm: '400px',
            xs: '400px',
          },
          position: 'relative',
          overflow: 'hidden',
          height: '100%',
        }}
      >
        <AnimatePresence mode="wait">
          {activeTab === 0 && (
            <motion.div
              key="signin"
              variants={slideVariants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{ duration: 0.5, ease: 'easeInOut' }}
              style={motionBoxStyle}
            >
              <SignInForm
                onSubmit={onSubmit}
                onForgotPassword={onForgotPassword}
                isLoading={isLoading}
                error={error}
                emailLabel={emailLabel}
                passwordLabel={passwordLabel}
                forgotPasswordLabel={forgotPasswordLabel}
                continueLabel={continueLabel}
                orLabel={orLabel}
                googleLabel={googleLabel}
                appleLabel={appleLabel}
                showPassword={showPasswords['signin-password']}
                onTogglePasswordVisibility={() =>
                  togglePasswordVisibility('signin-password')
                }
              />
            </motion.div>
          )}

          {activeTab === 1 && !userType && (
            <motion.div
              key="signup-type"
              variants={slideVariants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{ duration: 0.5, ease: 'easeInOut' }}
              style={{ ...motionBoxStyle, paddingTop: '20px' }}
            >
              <SignupTypeList onTypeSelect={handleTypeSelect} />
            </motion.div>
          )}

          {activeTab === 1 && userType && (
            <motion.div
              key={`signup-${userType}`}
              variants={slideVariants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{ duration: 0.5, ease: 'easeInOut' }}
              style={motionBoxStyle}
            >
              <SignUpForm
                userType={userType}
                onSubmit={onSubmit}
                isLoading={isLoading}
                error={error}
                showPassword={showPasswords['signup-password']}
                showConfirmPassword={showPasswords['confirm-password']}
                onTogglePasswordVisibility={(field) =>
                  togglePasswordVisibility(
                    field === 'password'
                      ? 'signup-password'
                      : 'confirm-password'
                  )
                }
                displayNameLabel={displayNameLabel}
                namePlaceholder={namePlaceholder}
                emailLabel={emailLabel}
                passwordLabel={passwordLabel}
                continueLabel={continueLabel}
                orLabel={orLabel}
                googleLabel={googleLabel}
                appleLabel={appleLabel}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </Box>
    </Box>
  );
};

export default AuthTabContent;
