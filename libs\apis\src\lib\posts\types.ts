export interface FeedPost {
  id: string;
  postType: string;
  content: string;
  postedAt: string;
  isLiked: boolean;
  commentsCount: number;
  likesCount: number;
  shareCount: number;
  repostCount: number;
  username: string;
  profileImageUrlThumbnail: string;
  publisherName: string;
  tags: string[];
  postMedias: Array<{
    id: string;
    mediaPath: string;
    mediaType: string;
    order: number;
    altText?: string;
    thumbnailPath?: string;
    width?: number;
    height?: number;
  }>;
  coverImagePath?: string;
  title?: string;
}

export interface FeedResponse {
  status: number;
  message: string;
  data: FeedPost[];
  stack: string | null;
}

export interface GetFeedParams {
  postTypes: string[];
  limit: number;
  offset: number;
  searchKeyword?: string;
}

export type Community = 'PROFESSIONAL' | 'PUBLIC' | 'BOTH';
export type Audience = 'CARDIAC_SURGEON' | 'CARDIOLOGIST' | 'BOTH';
export type PostStatus = 'draft' | 'published' | 'scheduled';

export interface MediaItem {
  mediaPath: string;
  mediaType: string;
  order: number;
  altText?: string;
}

export interface CreateMediaPostRequest {
  medias: MediaItem[];
  community: Community;
  audience: Audience;
  tags: string[];
  postStatus: PostStatus;
  postScheduleDate?: string;
  speciality?: string;
  content?: string;
}

export interface CreateArticlePostRequest {
  community: Community;
  audience: Audience | 'BOTH';
  tags: string[];
  postStatus: PostStatus;
  postScheduleDate?: string;
  title: string;
  content: string;
  body: string;
  coverImagePath?: string;
}
