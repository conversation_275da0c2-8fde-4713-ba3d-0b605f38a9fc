export function utilities(): string {
  return 'utilities';
}

export function removeAWSS3DomainFromURL(url: string): string {
  const basePath = 'public/';
  const index = url.indexOf(basePath);
  if (index === -1) return '';
  return url.substring(index);
}

export const getImageUrl = (imageUrl: string | null) => {
  if (!imageUrl) {
    // Return a default image from CDN
    return 'https://assets.dev.minicardiac.com/public/default-avatar.jpg';
  }

  // If it's already a full URL, check if it needs to be converted from S3 to CDN
  if (imageUrl.startsWith('http')) {
    // Convert S3 URLs to CDN URLs
    if (imageUrl.includes('s3-minicardiac-dev-assets.s3.amazonaws.com')) {
      return imageUrl.replace(
        's3-minicardiac-dev-assets.s3.amazonaws.com',
        'assets.dev.minicardiac.com'
      );
    }
    return imageUrl;
  }

  const path = imageUrl.startsWith('/') ? imageUrl.substring(1) : imageUrl;
  return `https://assets.dev.minicardiac.com/${path}`;
};

/**
 * Truncate a string to a maximum length.
 */
export function truncate(str: string | undefined, n: number): string {
  return str && str.length > n ? str.slice(0, n) + '…' : str || '';
}
