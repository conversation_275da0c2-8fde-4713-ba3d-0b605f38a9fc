'use client';
import { Box, useMediaQuery } from '@mui/material';
import Sidebar from '../navigations/Sidebar';
import BottomNavBar from '../navigations/BottomNavBar';
import { useTheme } from '@emotion/react';
import React from 'react';
import { useAuth } from '@minicardiac-client/apis';
import JoinSignupBanner from './JoinSignupBanner';

type ResponsiveLayoutProps = {
  topBar: React.ReactNode;
  children: React.ReactNode;
  rightSidebar?: React.ReactNode;
};

export default function ResponsiveLayout({
  topBar,
  children,
  rightSidebar,
}: ResponsiveLayoutProps) {
  const theme: any = useTheme();
  const { authState } = useAuth();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: isMobile ? 'column' : 'row',
        height: '100vh',
        width: '100%',
        justifyContent: 'center',
        backgroundColor: '#F3F4F6',
        position: 'relative',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          gap: { xs: '0px', sm: '20px', md: '20px', lg: '40px' },
          width: { xs: '100%', lg: '1280px' },
        }}
      >
        {/* Sidebar */}
        {!isMobile && (
          <Box
            sx={{
              height: '100vh',
              overflowY: 'auto',
              backgroundColor: 'white',
              width: { sm: 100, xs: 100, md: 224 },
              minWidth: { sm: 100, xs: 100, md: 224 },
              scrollbarWidth: 'none',
              '&::-webkit-scrollbar': {
                display: 'none',
              },
            }}
          >
            <Sidebar />
          </Box>
        )}

        {/* Main Section */}
        <Box
          sx={{
            display: 'flex',
            flex: 1,
            flexDirection: 'column',
            overflow: 'hidden',
            gap: '20px',
            position: 'relative',
          }}
        >
          {/* Top Bars */}
          <Box
            sx={{
              position: 'fixed',
              top: 0,
              zIndex: 100,
              width: '100%',
              backgroundColor: 'transparent',
              pr: { sm: '140px', md: '244px', lg: '0px' },
            }}
          >
            {topBar}
          </Box>

          {/* Scrollable Main Content */}
          <Box
            sx={{
              overflowY: 'auto',
              width: { smd: '100%', lg: '100%' },
              pr: { sm: '20px', lg: '0px' },
              scrollbarWidth: 'none',
              '&::-webkit-scrollbar': {
                display: 'none',
              },
              height: '100vh',
              mt: { xs: '290px', sm: '100px' },
            }}
          >
            {children}
          </Box>
        </Box>

        {/* Right Sidebar */}
        {!isMobile && rightSidebar}

        {!authState.isAuthenticated && <JoinSignupBanner />}

        {/* Bottom Nav - Mobile Only */}
        {isMobile && (
          <Box
            sx={{
              position: 'sticky',
              bottom: 0,
              zIndex: 10,
              backgroundColor: 'white',
            }}
          >
            <BottomNavBar />
          </Box>
        )}
      </Box>
    </Box>
  );
}
