import { Box, Typography, TextField, useMediaQuery } from '@mui/material';
import { Dayjs } from 'dayjs';

import CustomDateCalendar from './CustomDateCalendar';
import CustomToggleButtonGroup from '../buttons/CustomToggleButtonGroup';
import TimezoneAutocomplete from './TimezoneAutocomplete';
import { useTheme } from '@emotion/react';

interface ScheduleDateTimePickerProps {
  selectedDate: Dayjs | null;
  setSelectedDate: (date: Dayjs | null) => void;
  hour: string;
  setHour: (hour: string) => void;
  minute: string;
  setMinute: (minute: string) => void;
  meridian: 'AM' | 'PM';
  setMeridian: (meridian: 'AM' | 'PM') => void;
  timeFormat: '12h' | '24h';
  setTimeFormat: (format: '12h' | '24h') => void;
  selectedTimezone?: string;
  onTimezoneChange?: (timezone: string) => void;
}

const ScheduleDateTimePicker = ({
  selectedDate,
  setSelectedDate,
  hour,
  setHour,
  minute,
  setMinute,
  meridian,
  setMeridian,
  timeFormat,
  setTimeFormat,
  selectedTimezone,
  onTimezoneChange,
}: ScheduleDateTimePickerProps) => {
  const theme: any = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('lg'));

  return (
    <Box
      display="flex"
      sx={{
        gap: { xs: '40px', md: '20px', lg: '40px' },
        justifyContent: 'center',
        flexDirection: { xs: 'column', md: 'row' },
        mb: { xs: '150px', sm: '0px' },
      }}
    >
      {/* Date Selector */}
      <Box
        display={'flex'}
        sx={{
          boxShadow: '0px 20px 40px -4px rgba(0, 0, 0, 0.1)',
          borderRadius: '8px',
          width: { md: '350px', lg: '480px' },
          flexDirection: { xs: 'column', lg: 'row' },
          alignItems: { xs: 'center', lg: 'start' },
          height: { md: '420px', lg: '289px' },
        }}
      >
        <Box
          sx={{
            width: { xs: '100%', lg: '160px' },
            height: '100%',
            p: '16px',
            display: 'flex',
            flexDirection: { xs: 'row', lg: 'column' },
            justifyContent: { xs: 'space-between', lg: 'center' },
          }}
        >
          <Typography
            variant="caption"
            sx={{
              fontWeight: 700,
              fontSize: '12px',
              color: '#A3A3A3',
            }}
          >
            SELECT DATE
          </Typography>

          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              height: '100%',
              alignItems: 'start',
            }}
          >
            {selectedDate && (
              <Box sx={{}}>
                <Typography
                  sx={{
                    fontFamily: 'Plus Jakarta Sans',
                    fontWeight: 700,
                    fontSize: '20px',
                  }}
                >
                  {selectedDate.format('MMM D')}
                </Typography>
                <Typography
                  sx={{
                    fontFamily: 'Plus Jakarta Sans',
                    fontWeight: 500,
                    fontSize: '16px',
                    color: '#A3A3A3',
                  }}
                >
                  {selectedDate.format('dddd')}
                </Typography>
              </Box>
            )}
          </Box>
        </Box>
        <CustomDateCalendar
          selectedDate={selectedDate}
          setSelectedDate={setSelectedDate}
        />
      </Box>

      {/* Time Selector */}
      <Box
        borderRadius={2}
        p={2}
        sx={{
          boxShadow: '0px 20px 40px -4px rgba(0, 0, 0, 0.1)',
          display: 'flex',
          flexDirection: 'column',
          gap: '45px',
          width: { md: '350px', lg: '460px' },
          height: { md: '420px', lg: '289px' },
          justifyContent: { md: 'center', lg: 'start' },
        }}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: { md: 'center', lg: 'start' },
          }}
        >
          <Typography
            variant="caption"
            sx={{
              fontWeight: 700,
              fontSize: '12px',
              color: '#A3A3A3',
            }}
          >
            SELECT TIME
          </Typography>

          {isSmallScreen && (
            <Box
              sx={{
                width: '94px',
              }}
            >
              <CustomToggleButtonGroup
                options={['12h', '24h']}
                selected={timeFormat}
                onChange={setTimeFormat}
              />
            </Box>
          )}
        </Box>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            flexDirection: 'column',
            gap: '41px',
          }}
        >
          {/* Timezone */}
          <TimezoneAutocomplete
            selectedTimezone={selectedTimezone}
            onTimezoneChange={onTimezoneChange}
          />
          <Box
            sx={{
              display: 'flex',
              gap: '24px',
              alignItems: 'center',
              height: '46px',
            }}
          >
            {/* Hour / Minute */}
            <Box display="flex" alignItems="center" gap={'4px'}>
              <TextField
                label="Hour"
                placeholder="8"
                value={hour}
                onChange={(e) => setHour(e.target.value)}
                size="small"
                inputProps={{
                  maxLength: 2,
                  style: {
                    fontWeight: hour ? 'bold' : 'normal',
                  },
                }}
                InputLabelProps={{
                  shrink: true,
                }}
                sx={{ width: '88px', height: '45px' }}
              />

              <Typography fontWeight="bold">:</Typography>

              <TextField
                label="Minute"
                placeholder="15"
                value={minute}
                onChange={(e) => setMinute(e.target.value)}
                size="small"
                InputLabelProps={{
                  shrink: true,
                }}
                inputProps={{
                  maxLength: 2,
                  style: {
                    fontWeight: minute ? 'bold' : 'normal',
                  },
                }}
                sx={{ width: '88px', height: '45px' }}
              />

              {timeFormat === '12h' && (
                <Box
                  sx={{
                    ml: '8px',
                    display: 'flex',
                    flexDirection: 'column',
                  }}
                >
                  {(['AM', 'PM'] as const).map((val) => (
                    <Typography
                      key={val}
                      onClick={() => setMeridian(val)}
                      sx={{
                        cursor: 'pointer',
                        fontWeight: meridian === val ? 700 : 500,
                        color: meridian === val ? '#A24295' : '#A3A3A3',
                        userSelect: 'none',
                        fontSize: '16px',
                      }}
                    >
                      {val}
                    </Typography>
                  ))}
                </Box>
              )}
            </Box>

            {!isSmallScreen && (
              <Box
                sx={{
                  width: '94px',
                }}
              >
                <CustomToggleButtonGroup
                  options={['12h', '24h']}
                  selected={timeFormat}
                  onChange={setTimeFormat}
                />
              </Box>
            )}
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default ScheduleDateTimePicker;
