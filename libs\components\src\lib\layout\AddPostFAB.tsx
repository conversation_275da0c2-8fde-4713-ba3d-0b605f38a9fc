'use client';
import { useState } from 'react';
import { Box, Typography, useMediaQuery } from '@mui/material';
import AddIcon from '../Icons/FeedIcons/AddIcon';
import TextIcon from '../Icons/FeedIcons/TextIcon';
import MediaIcon from '../Icons/FeedIcons/MediaIcon';
import ArticleIcon from '../Icons/FeedIcons/ArticleIcon';
import PollIcon from '../Icons/FeedIcons/PollIcon';
import QuestionIcon from '../Icons/FeedIcons/QuestionIcon';
import ScheduledIcon from '../Icons/FeedIcons/ScheduledIcon';
import DraftIcon from '../Icons/FeedIcons/DrafsIcon';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { motion, AnimatePresence } from 'framer-motion';
import { usePostDialogStore } from '@minicardiac-client/apis';

interface AddPostFABProps {
  onPostCreated?: () => void;
}

const iconOptions = [
  { icon: <DraftIcon hoverFill="white" />, label: 'Draft' },
  { icon: <ScheduledIcon hoverFill="white" />, label: 'Schedule' },
  'divider',
  { icon: <QuestionIcon hoverFill="white" />, label: 'Question' },
  { icon: <PollIcon hoverFill="white" />, label: 'Poll' },
  { icon: <ArticleIcon hoverFill="white" />, label: 'Article' },
  { icon: <MediaIcon hoverFill="white" />, label: 'Media' },
  { icon: <TextIcon hoverFill="white" />, label: 'Text' },
];

const containerVariants = {
  hidden: {},
  visible: {
    transition: {
      staggerChildren: 0.08,
      delayChildren: 0.1,
    },
  },
  exit: {
    transition: {
      staggerChildren: 0.05,
      staggerDirection: -1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 40 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.3 } },
  exit: { opacity: 0, y: 40, transition: { duration: 0.2 } },
};

export default function AddPostFAB({ onPostCreated }: AddPostFABProps) {
  const isSmallScreen = useMediaQuery('(max-width:1100px)');
  const [open, setOpen] = useState(false);
  const { setActiveDialog } = usePostDialogStore();

  if (!isSmallScreen) return null;

  return (
    <>
      {open && (
        <Box
          onClick={() => setOpen(false)}
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100vw',
            height: '100vh',
            backgroundColor: 'rgba(0,0,0,0.3)',
            zIndex: 1200,
          }}
        />
      )}

      <Box
        sx={{
          position: 'fixed',
          bottom: 92,
          right: 16,
          zIndex: 1300,
          borderRadius: '8px',
          overflow: 'auto',
          maxHeight: 'calc(100vh - 120px)',
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
          '&::-webkit-scrollbar': { display: 'none' },
        }}
      >
        <Box
          sx={{
            backgroundColor: '#A24295',
            borderRadius: '8px',
            overflow: 'auto',
          }}
        >
          <AnimatePresence>
            {open && (
              <motion.div
                key="fab-options"
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
              >
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    pb: '8px',
                    gap: '16px',
                  }}
                >
                  {iconOptions.map((item, index) =>
                    typeof item === 'string' ? (
                      <Box
                        key={`divider-${index}`}
                        sx={{
                          width: '80%',
                          borderBottom: '1px solid #BE7BB5',
                          my: '4px',
                        }}
                      />
                    ) : (
                      <motion.div key={item.label} variants={itemVariants}>
                        <Box
                          display="flex"
                          flexDirection="column"
                          alignItems="center"
                          justifyContent="center"
                          px={'12px'}
                          py={'8px'}
                          gap={'8px'}
                          width={'84px'}
                          height={'74px'}
                          sx={{
                            cursor: 'pointer',
                            transition: '0.1s',
                            '&:hover': {
                              backgroundColor: '#922C7D',
                            },
                            backgroundColor: '#A24295',
                          }}
                          onClick={() => {
                            setActiveDialog(item.label);
                            setOpen(false);
                          }}
                        >
                          <Box sx={{ width: '36px', height: '36px' }}>
                            {item.icon}
                          </Box>
                          <Typography
                            sx={{
                              fontSize: '14px',
                              fontWeight: 500,
                              color: 'white',
                            }}
                          >
                            {item.label}
                          </Typography>
                        </Box>
                      </motion.div>
                    )
                  )}
                </Box>
              </motion.div>
            )}
          </AnimatePresence>

          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              py: '6px',
              width: open ? '84px' : '60px',
              height: open ? '36px' : '60px',
            }}
            onClick={() => setOpen((prev) => !prev)}
          >
            <motion.div
              animate={{ rotate: open ? 0 : 180 }}
              transition={{ duration: 0.3 }}
            >
              {open ? (
                <ExpandMoreIcon sx={{ color: 'white' }} />
              ) : (
                <AddIcon hoverFill="white" />
              )}
            </motion.div>
          </Box>
        </Box>
      </Box>
    </>
  );
}
