import { useMutation } from '@tanstack/react-query';
import axios from 'axios';
import { UploadImageError } from './types.js';
import { ENTITY_TYPES } from './constants.js';
import { auth } from '../firebase/firebase-client.js';
import { axiosInstance } from '../http-client.js';

interface ProfileImageUploadData {
  file: File;
  introductoryStatement: string;
  title: string;
  qualifications: string;
  jobTitle: string;
  employerId: string;
}

export const useProfileImageUpload = () => {
  return useMutation<string, UploadImageError, ProfileImageUploadData>({
    mutationFn: async ({ file, introductoryStatement, title, qualifications, jobTitle, employerId }: ProfileImageUploadData) => {
      if (!auth || !auth.currentUser) {
        throw {
          message: 'User not authenticated',
          code: 'auth/not-authenticated'
        } as UploadImageError;
      }

      try {
        console.log('Requesting pre-signed URL for upload...');
        const { data } = await axiosInstance.post(
          '/api/v1/utils/generate-upload-url',
          {
            mediaType: file.type,
            entityType: ENTITY_TYPES.PROFILE
          }
        );
        console.log('Pre-signed URL response:', data);

        let uploadUrl = '';
        let fileUrl = '';

        if (data) {
          if (data.url) {
            uploadUrl = data.url;
            fileUrl = data.fileUrl || '';

            if (data.fields) {
              const key = data.fields.key || '';
              if (key && !fileUrl) {
                fileUrl = key;
              }
            }
          }
          else if (data.bucket && data.key) {
            uploadUrl = `https://${data.bucket}.s3.amazonaws.com/${data.key}`;
            fileUrl = data.key || '';
          }
        }

        if (!uploadUrl) {
          throw new Error('Failed to get upload URL from server: Missing URL in response');
        }

        // 2. Upload to pre-signed URL (direct to S3)
        const formData = new FormData();

        if (data.fields) {
          Object.entries(data.fields).forEach(([fieldKey, fieldValue]) => {
            formData.append(fieldKey, fieldValue as string);
          });
        }

        formData.append('file', file);

        // Set a timeout to prevent policy expiration issues
        console.log('Uploading to S3 at URL:', uploadUrl);
        try {
          const response = await axios.post(uploadUrl, formData, {
            timeout: 45000, // 45 seconds (less than the 60-second policy timeout)
          });
          
          console.log('S3 upload successful, status:', response.status);
          if (response.status !== 200 && response.status !== 201 && response.status !== 204) {
            console.error('S3 upload error:', response.data);
          }
        } catch (error: unknown) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error during file upload';
          console.error('Upload error:', errorMessage);
          throw new Error(`File upload failed: ${errorMessage}`);
        }

        if (!fileUrl) {
          // Get just the path/key from the S3 data
          fileUrl = data.key || (data.fields && data.fields.key) || '';
        }

        if (!fileUrl) {
          throw new Error('Failed to determine image URL for profile update');
        }

        // The fileUrl at this point should already be just the path/key
        // Ensure it's properly formatted for the backend
        const fileKey = fileUrl;
        console.log('File key to be sent to backend:', fileKey);

        // Use the correct endpoint for the API
        console.log('Sending profile data to backend with image path:', fileKey);
        const { data: profileData } = await axiosInstance.post(
          '/api/v1/onboarding/profile-setup/specialist',
          {
            introductoryStatement,
            profileImageUrl: fileKey,
            profileImageUrlThumbnail: fileKey,
            title,
            qualifications,
            jobTitle,
            employerId
          }
        );
        console.log('Profile update response:', profileData);

        // Get path from response or use original path
        const imagePath = profileData.profileImageUrl || fileUrl;
        
        // Convert path to full URL for display
        // If it's already a full URL, leave it as is
        let returnUrl = imagePath;
        if (imagePath && !imagePath.startsWith('http')) {
          // It's just a path, add the base URL
          returnUrl = `https://assets.dev.minicardiac.com/${imagePath.startsWith('/') ? imagePath.slice(1) : imagePath}`;
        } else if (returnUrl && returnUrl.includes('s3-minicardiac-dev-assets.s3.amazonaws.com')) {
          // If it's still an S3 URL, convert to CDN URL
          returnUrl = returnUrl.replace('s3-minicardiac-dev-assets.s3.amazonaws.com', 'assets.dev.minicardiac.com');
        }
        
        return returnUrl;
      } catch (error) {
        const err = error as {
          response?: { status: number },
          request?: unknown,
          message?: string,
          code?: string
        };

        if (err.response) {
          if (err.response.status === 401) {
            throw {
              message: 'Authentication required. Please sign in.',
              code: 'auth/not-authenticated'
            } as UploadImageError;
          } else if (err.response.status === 440) {
            throw {
              message: 'Your session expired. Please try again.',
              code: 'auth/session-expired'
            } as UploadImageError;
          } else if (err.response.status >= 500) {
            throw {
              message: 'Server error. Please try again later.',
              code: 'server-error'
            } as UploadImageError;
          }
        } else if (err.request) {
          throw {
            message: 'Network error. Please check your connection and try again.',
            code: 'network-error'
          } as UploadImageError;
        }

        throw {
          message: err.message || 'Failed to upload profile image',
          code: err.code || 'upload-failed'
        } as UploadImageError;
      }
    }
  });
};

