'use client';

import { Box, Typography } from '@mui/material';
import { useRouter } from 'next/navigation';
import { useCallback, useState } from 'react';
import { FullPageLoader } from '../full-page-loader';
import { useTranslations } from 'next-intl';
import { HighlightHtml } from '../common/HighlightHtml';
import { useFeedSearchStore } from '../store/useFeedSearchStore';
import { generateSlug } from '@minicardiac-client/utilities';

interface ContentMediaPostCaptionProps {
  MAX_LINES?: number;
  content: string;
  showMore?: boolean;
  setOpenDialog: React.Dispatch<React.SetStateAction<boolean>>;
  mediaId: string;
}

export const ContentMediaPostCaption = ({
  MAX_LINES,
  content,
  showMore = false,
  setOpenDialog,
  mediaId,
}: ContentMediaPostCaptionProps) => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const t = useTranslations('mediaPost');
  const searchKeyword = useFeedSearchStore((state) => state.searchKeyword);

  const handleClick = useCallback(() => {
    const selection = window.getSelection();
    const isTextSelected = selection && selection.toString().length > 0;

    if (isTextSelected) return;

    const slug = generateSlug(content);

    setLoading(true);
    router.push(`/feed/media/${mediaId}/${slug}`);
  }, [content, mediaId, router]);

  return (
    <>
      <FullPageLoader
        open={loading}
        message={t('loadingMessage')}
        sx={{
          backgroundColor: '#1E1E1E40',
        }}
      />

      <Box
        mt={{ xs: '20px', sm: '16px' }}
        sx={{ position: 'relative', cursor: 'pointer' }}
        onClick={handleClick}
      >
        <Typography
          component="div"
          sx={{
            fontSize: '12px',
            lineHeight: '18px',
            color: '#1E1E1E',
            display: MAX_LINES ? '-webkit-box' : 'block',
            ...(MAX_LINES && {
              WebkitLineClamp: MAX_LINES,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }),
            userSelect: 'text',
          }}
        >
          <HighlightHtml html={content} keyword={searchKeyword} />
        </Typography>

        {!showMore && (
          <Typography
            sx={{
              fontSize: { xs: '12px', sm: '14px' },
              fontWeight: { xs: 600, sm: 500 },
              color: '#A24295',
              cursor: 'pointer',
              userSelect: 'none',
              display: 'inline-block',
              lineHeight: '18px',
              mt: '4px',
            }}
            onClick={(e) => {
              e.stopPropagation();
              setOpenDialog(true);
            }}
          >
            {t('seeMore')}
          </Typography>
        )}
      </Box>
    </>
  );
};
