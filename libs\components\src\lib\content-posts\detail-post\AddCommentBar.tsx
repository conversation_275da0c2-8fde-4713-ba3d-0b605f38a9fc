import { Box } from '@mui/material';
import AddCommentInput from '../../comments/AddCommentInput';

interface AddCommentBarProps {
  postId: string;
}

const AddCommentBar = ({ postId }: AddCommentBarProps) => {
  return (
    <Box
      sx={{
        position: {
          xs: 'static', // not fixed on small screens
          sm: 'fixed', // fixed on sm and up
        },
        bottom: {
          sm: 0,
        },
        left: {
          sm: 0,
        },
        right: {
          sm: 0,
        },
        bgcolor: {
          xs: 'transparent',
          sm: '#fff',
        },
        boxShadow: {
          xs: 'none',
          sm: '0px -4px 20px rgba(0,0,0,0.08)',
        },
        display: 'flex',
        gap: '12px',
        alignItems: 'flex-start',
        px: {
          sm: '40px',
        },
        py: {
          sm: '20px',
        },
        zIndex: 900,
      }}
    >
      <AddCommentInput
        postId={postId}
        sx={{
          width: {
            xs: '100%',
            sm: 824,
          },
        }}
      />
    </Box>
  );
};

export default AddCommentBar;
