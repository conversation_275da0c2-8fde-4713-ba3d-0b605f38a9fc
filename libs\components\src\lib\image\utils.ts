import { axiosInstance } from '../../../../apis/src/lib/http-client';

export function getRatio(ratio = '1/1'): string | undefined {
  return {
    '4/3': 'calc(100% / 4 * 3)',
    '3/4': 'calc(100% / 3 * 4)',
    '6/4': 'calc(100% / 6 * 4)',
    '4/6': 'calc(100% / 4 * 6)',
    '16/9': 'calc(100% / 16 * 9)',
    '9/16': 'calc(100% / 9 * 16)',
    '21/9': 'calc(100% / 21 * 9)',
    '9/21': 'calc(100% / 9 * 21)',
    '1/1': '100%',
  }[ratio];
}

/**
 * Upload a single image file to S3 and return the S3 key (path).
 * @param file The image file to upload
 * @returns Promise<string> The S3 key/path of the uploaded image
 */
export async function uploadImageToS3(file: File): Promise<string> {

  const { data: presignResponse } = await axiosInstance.post('/utils/generate-upload-url', {
    mediaType: file.type,
    entityType: 'post',
  });
  const { url, fields } = presignResponse.data;


  const formData = new FormData();
  Object.entries(fields).forEach(([k, v]) => formData.append(k, v as string));
  formData.append('file', file);

  const uploadRes = await fetch(url, {
    method: 'POST',
    body: formData,
  });
  if (!uploadRes.ok) throw new Error('Failed to upload file to S3');
  return fields.key;
}
