import { formatDistanceToNowStrict, parse, isValid } from 'date-fns';

export function getTimeAgo(dateString: string): string {
  let parsedDate: Date | null = null;

  // Try parsing MM/dd/yyyy format (e.g., "7/15/2025")
  parsedDate = parse(dateString, 'M/d/yyyy', new Date());
  if (!isValid(parsedDate)) {
    // Fallback to native Date parsing for ISO / Postgres timestamp
    parsedDate = new Date(dateString);
  }

  if (!isValid(parsedDate)) return 'Invalid date';

  return formatDistanceToNowStrict(parsedDate, { addSuffix: true });
}
