import { useState } from 'react';

const NetworkIcon = ({
  fill = 'currentColor',
  hoverFill = '#A24295',
  size = 24,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const currentFill = isHovered ? hoverFill : fill;

  return (
    <svg
      width="24"
      height="21"
      viewBox="0 0 24 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <path
        d="M11.9537 19.9144C10.4335 19.8974 8.96104 19.6639 7.59202 18.9586C6.2886 18.287 5.54083 17.3452 5.72223 15.7513C5.95782 13.6815 6.40715 11.7036 7.64072 9.98645C8.9626 8.1464 10.6621 7.01004 13.0394 7.48026C14.8157 7.83158 15.9841 9.04716 16.8817 10.532C17.9813 12.3509 18.4159 14.3628 18.4456 16.4667C18.4581 17.3534 17.9694 18.004 17.2782 18.4956C15.777 19.5633 14.0527 19.8693 12.2583 19.9141C12.1569 19.9166 12.0553 19.9144 11.9537 19.9144Z"
        fill={currentFill}
        stroke="white"
        strokeWidth="0.5"
      />
      <path
        d="M8.01672 8.34123C5.63464 10.8625 4.90086 13.9338 4.93521 17.257C3.67605 17.5568 0.987711 16.82 0.289247 15.5777C0.016967 15.0934 -0.0300409 14.4101 0.0149295 13.8326C0.190124 11.5822 0.874716 9.53156 2.67592 8.01795C4.27374 6.67519 6.58478 6.79924 8.01672 8.34123Z"
        fill={currentFill}
      />
      <path
        d="M19.1703 17.368C19.279 14.031 18.5784 11.0845 16.3625 8.63636C16.0498 8.29088 16.224 8.11962 16.4907 7.91045C17.9368 6.77604 19.996 6.79156 21.4352 8.02917C23.0531 9.42053 23.8689 11.229 23.974 13.3557C23.996 13.8011 23.9351 14.2535 23.9865 14.694C24.0593 15.3166 23.7905 15.7459 23.3327 16.1002C22.1067 17.0491 20.6869 17.3284 19.1703 17.368Z"
        fill={currentFill}
      />
      <path
        d="M8.82048 3.26013C8.83821 1.42061 10.254 -0.0094948 12.048 4.74737e-05C13.9553 0.0101993 15.3629 1.42365 15.3504 3.31616C15.3384 5.13214 13.8983 6.55263 12.0848 6.53739C10.2034 6.52159 8.8026 5.11526 8.82048 3.26013Z"
        fill={currentFill}
      />
      <path
        d="M5.21212 6.68053C3.68406 6.69032 2.52919 5.53838 2.51573 3.99102C2.50285 2.51086 3.68553 1.3206 5.17569 1.31399C6.69509 1.30726 7.87685 2.48831 7.87977 4.01646C7.88261 5.51117 6.72137 6.67087 5.21212 6.68053Z"
        fill={currentFill}
      />
      <path
        d="M18.8839 6.68833C17.4021 6.68853 16.2146 5.50839 16.2032 4.02424C16.1916 2.51386 17.3957 1.31404 18.9113 1.32577C20.4121 1.33738 21.5902 2.50643 21.5923 3.98611C21.5945 5.50078 20.4044 6.68812 18.8839 6.68833Z"
        fill={currentFill}
      />
    </svg>
  );
};

export default NetworkIcon;
