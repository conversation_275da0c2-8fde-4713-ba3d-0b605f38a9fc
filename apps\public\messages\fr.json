{"signin": {"title": "Bienvenue chez MiniCardiac", "subtitle": "Le cœur des soins cardiologiques", "welcomeTo": "Bienvenue chez", "emailLabel": "E-mail", "passwordLabel": "Mot de passe", "forgotPassword": "Mot de passe oublié ?", "continue": "<PERSON><PERSON><PERSON>", "signIn": "Se connecter", "signUp": "S'inscrire", "or": "OU", "google": "Continuer avec Google", "apple": "Continuer avec Apple"}, "signup": {"title": "Bienvenue chez MiniCardiac", "subtitle": "Le cœur des soins cardiologiques", "welcomeTo": "Bienvenue chez", "displayName": "Nom d'affichage", "name": "Nom", "emailLabel": "E-mail", "passwordLabel": "Mot de passe", "forgotPassword": "Mot de passe oublié ?", "continue": "<PERSON><PERSON><PERSON>", "signIn": "Se connecter", "signUp": "S'inscrire", "or": "OU", "signUpAs": "S'inscrire en tant que", "google": "Continuer avec Google", "apple": "Continuer avec Apple", "enterOtpSent": "Entrez l'OTP envoyé à votre adresse e-mail enregistrée", "resendOtp": "Renvoyer l'OTP", "otpLabel": "OTP", "otpPlaceholder": "Entrez l'OTP", "otpInputPlaceholder": "OTP à 6 chiffres", "otpInstruction": "Entrez l'OTP envoyé à votre adresse e-mail enregistrée", "otpResentSuccess": "OTP renvoyé avec succès !", "otpVerifiedSuccess": "OTP vérifié avec succès !", "otpVerificationFailed": "Échec de la vérification. Aucun jeton d'authentification reçu.", "authenticationSuccessRedirect": "Authentification réussie ! Redirection...", "otpResent": "Un nouvel OTP a été envoyé à votre e-mail", "sending": "Envoi en cours...", "verifying": "Vérification...", "missingEmailParam": "Paramètre e-mail manquant. Veuillez retourner à la page d'inscription.", "redirectingToProfile": "Redirection vers votre profil...", "profileSetupForm": {"addProfilePicture": "+ Ajouter une photo de profil", "introductoryStatement": "Déclaration introductive", "introductoryStatementPlaceholder": "Écrivez quelques lignes pour vous présenter aux autres membres de la communauté. Si vous le souhaitez - c'est entièrement optionnel !", "loadingText": "Chargement...", "saveAndContinue": "Enregistrer et continuer", "addPhotoLabel": "+ Ajouter une photo", "changePhotoLabel": "Changer la photo", "removePhotoLabel": "Supprimer la photo", "characters": "caractères"}, "loading": "Chargement...", "genericErrorMessage": "Quelque chose s'est mal passé. Veuillez réessayer plus tard."}, "signUpPublicFlow": {"subtitlePublicFlow": "Pour vous aider à d<PERSON><PERSON><PERSON>, configurons votre profil public !"}, "feedPage": {"feed": "Fil", "minicardiac": "MiniCardiac"}, "feedNavigation": {"tag": "Étiquette", "tags": "Étiquettes", "follow": "Suivre", "following": "<PERSON><PERSON><PERSON>", "savedPosts": "Publications enregistrées", "addFolder": "Ajouter un dossier", "search": "Recherche", "filter": "<PERSON><PERSON><PERSON>", "searchPlaceholder": "Rechercher par mot-clé, auteur ou étiquette", "voiceSearch": "Recherche vocale", "profile": "Profil", "myAccount": "Mon compte", "logout": "Déconnexion", "followed": "<PERSON><PERSON><PERSON>", "suggested": "<PERSON><PERSON><PERSON><PERSON>", "browseAllTags": "Parcourir toutes les étiquettes", "yourFolders": "Vos dossiers", "noFolders": "Aucun dossier ajouté pour l’instant.", "clickToGetStarted": "Cliquez ci-dessous pour commencer !"}, "tagsPage": {"yourTags": "Vos étiquettes", "suggestedTags": "Sugg<PERSON><PERSON><PERSON>", "seeAll": "Voir tout", "followedTag": "Étiquette suivie {tag}", "tags": "Étiquettes", "minicardiac": "MiniCardiac", "suggested": "Sugg<PERSON><PERSON><PERSON>", "followed": "Suivies", "browseAllTags": "Parcourir toutes les étiquettes"}, "comment": {"showMore": "Afficher plus", "like": "<PERSON><PERSON>aime", "reply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pinAnswer": "<PERSON><PERSON><PERSON> comme réponse", "noComments": "Aucun commentaire pour l’instant."}, "questionPost": {"questionPost": "Publication de question", "minicardiac": "MiniCardiac", "like": "<PERSON>'aime", "comment": "Commentaire", "repost": "Repartager", "share": "Partager", "noComments": "Aucun commentaire pour le moment.", "noAnswerTitle": "Aucune réponse pour l’instant", "noAnswerSubtitle": "Partagez vos idées !", "loadingQuestion": "Chargement de la question...", "title": "Nouvelle question", "drafts": "Brouillons", "editorLabel": "Votre question", "editorPlaceholder": "Vous êtes curieux de quelque chose ? Posez votre question à vos pairs ici.", "tagsLabel": "Tags", "tagsPlaceholder": "#Chirurgie #Valve", "community": "Communauté", "audience": "Audience", "cancel": "Annuler", "featuredAnswer": "Réponse mise en avant", "unpin": "<PERSON><PERSON><PERSON><PERSON>", "unpinSuccess": "Commentaire détaché avec succès", "unpinError": "Échec du détachement du commentaire", "menu": {"schedule": "Programmer", "draft": "Enregistrer le brouillon", "sponsor": "Ajouter à la file de parrainage"}, "toast": {"posted": "<PERSON><PERSON><PERSON>"}}, "pollPost": {"loadingPoll": "Chargement du sondage...", "seeMore": "Voir plus", "selectCustomAnswers": "Choisir parmi les réponses personnalisées", "pollTitle": "Quel est votre avis sur la nouvelle mise à jour ?", "retractVote": "Retirer le vote", "customAnswerPlaceholder": "Vous avez une autre suggestion ? Écrivez-la ici !", "submit": "So<PERSON><PERSON><PERSON>", "newPollPost": "Nouveau sondage", "drafts": "Brouillons", "createPoll": "<PERSON><PERSON><PERSON> un sondage", "addDetails": "Ajouter des détails", "cancel": "Annuler", "next": "Suivant", "schedule": "Programmer", "saveDraft": "Enregistrer le brouillon", "sponsorshipQueue": "Ajouter à la file de parrainage", "pollPosted": "Sondage publié", "caption": "Légende", "captionPlaceholder": "Curieux de voir comment la pratique clinique évolue — merci de participer !", "tags": "Étiquettes", "tagsPlaceholder": "#Chirurgie #Recherche", "community": "Communauté", "audience": "Audience", "questionLabel": "Question du sondage", "questionPlaceholder": "Vous avez une question à poser à votre public ? Posez-la ici.", "option": "Option", "addOption": "Ajouter une option", "customAnswer": "Autoriser les réponses personnalisées"}, "articlePost": {"loading": "Chargement de l’article...", "readFull": "Lire l’article complet", "newArticlePost": "Nouvel article", "drafts": "Brouillons", "stepCompose": "Rédiger l’article", "stepDetails": "Ajouter des détails", "editorLabel": "Votre article", "editorPlaceholder": "Rédigez votre contenu ici !", "cancel": "Annuler", "next": "Suivant", "schedule": "Programmer", "saveDraft": "Enregistrer le brouillon", "addToSponsorship": "Ajouter à la file de parrainage", "articlePosted": "Article publié", "tapToSelectThumbnail": "Appuyez pour sélectionner une miniature", "clickToAddThumbnail": "Cliquez pour ajouter une miniature", "dragAndDrop": "Ou faites glisser et déposez", "title": "Titre", "titlePlaceholder": "Ajoutez un titre à votre article", "summary": "Résumé", "summaryPlaceholder": "Ajou<PERSON>z un résumé pour votre article !", "tags": "Étiquettes", "community": "Communauté", "audience": "Audience"}, "mediaPost": {"loading": "Chargement de la publication média...", "readMore": "Lire plus", "comments": "Commentaires", "like": "<PERSON><PERSON>aime", "repost": "<PERSON><PERSON><PERSON>", "share": "Partager", "details": "Détails", "commentPlaceholder": "Qu’en pensez-vous ?", "seeMore": "Voir plus", "loadingMessage": "Chargement de la publication média...", "newMediaPost": "Nouvelle publication média", "drafts": "Brouillons", "addMedia": "Ajouter un média", "addDetails": "Ajouter des détails", "tags": "Étiquettes", "tagsPlaceholder": "#Chirurgie #Valve", "community": "Communauté", "audience": "Audience", "cancel": "Annuler", "next": "Suivant", "schedule": "Programmer", "saveDraft": "Enregistrer le brouillon", "addToSponsorship": "Ajouter à la file de parrainage", "posted": "<PERSON><PERSON><PERSON>", "clickToAddMedia": "Cliquez pour ajouter un média", "orDragAndDrop": "Ou faites glisser et déposez", "reorderHint": "Cliquez et faites glisser les images pour les réorganiser", "caption": "Légende", "captionPlaceholder": "Ajoutez une légende à votre publication !"}, "schedulePost": {"title": "Programmer la publication", "return": "Retourner à la publication", "schedule": "Programmer", "toast": {"scheduled": "Publication programmée"}}, "savePost": {"title": "Enregistrer la publication", "folderLabel": "Nom du dossier", "folderPlaceholder": "Nouveau nom de dossier", "cancel": "Annuler", "save": "Enregistrer", "addFolder": "Ajouter un dossier"}, "newFolderDialog": {"title": "Ajouter un nouveau dossier", "folderLabel": "Nom du dossier", "folderPlaceholder": "Le nom de votre nouvelle collection", "cancel": "Annuler", "save": "Enregistrer"}, "headers": {"minicardiac": "MiniCardiac"}}