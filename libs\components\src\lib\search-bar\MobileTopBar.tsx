'use client';

import { useEffect, useState } from 'react';
import { Box, Button, Typography } from '@mui/material';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';

import { FOLDER_ICON, MINICARDIAC_TRADEMARK } from '../auth';
import { getDecodedToken } from '@minicardiac-client/utilities';
import { useAuth, usePostDialogStore } from '@minicardiac-client/apis';
import { FeedProfile } from '../navigations/FeedProfile';
import { BackButton } from '../buttons/Backbutton';
import { useTranslations } from 'next-intl';
import { Iconify } from '../iconify';
import { ProfileSidebar } from '../navigations/ProfileSidebar';

type MobileTopBarProps = {
  variant?: 'default' | 'tags' | 'filter' | 'saved-posts';
};

export default function MobileTopBar({
  variant = 'default',
}: MobileTopBarProps) {
  const [userDetails, setUserDetails] = useState<{
    name: string;
    photoURL: string;
  } | null>(null);
  const t = useTranslations('feedNavigation');
  const { authState } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isFollowing, setIsFollowing] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const { setActiveDialog } = usePostDialogStore();

  useEffect(() => {
    (async () => {
      const decoded = await getDecodedToken();
      setUserDetails({
        name: decoded.name,
        photoURL: authState.user?.photoURL || '',
      });
    })();
  }, [authState]);

  const handleFollowClick = () => {
    setIsFollowing((prev) => !prev);
  };

  const tagName = decodeURIComponent(searchParams?.get('hashtag') || '');
  const folderName = decodeURIComponent(searchParams?.get('folder') || '');

  if (variant === 'default') {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          backgroundColor: 'white',
          px: 2,
          py: 2.75,
          height: '72px',
        }}
      >
        <Image
          src={MINICARDIAC_TRADEMARK}
          alt="MiniCard Icon"
          width={181}
          height={28}
        />
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            cursor: 'pointer',
          }}
          onClick={() => setIsDropdownOpen((prev) => !prev)}
        >
          <FeedProfile userDetails={userDetails} />

          <Iconify
            icon="mdi:chevron-down"
            style={{
              fontSize: 24,
              color: '#A24295',
              transition: 'transform 0.3s ease',
              transform: isDropdownOpen ? 'rotate(180deg)' : 'rotate(0deg)',
            }}
          />

          <ProfileSidebar
            open={isDropdownOpen}
            onClose={() => setIsDropdownOpen(false)}
          />
        </Box>
      </Box>
    );
  }

  if (variant === 'tags') {
    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          backgroundColor: 'white',
          px: 2,
          py: 1.5,
          gap: 2,
        }}
      >
        <BackButton onClick={() => router.back()} />
        <Typography fontSize={20} fontWeight={500}>
          {t('tags')}
        </Typography>
      </Box>
    );
  }

  if (variant === 'filter') {
    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          backgroundColor: 'white',
          px: 2,
          py: 1.5,
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <BackButton onClick={() => router.back()} />

          {tagName && (
            <Typography fontSize={20} fontWeight={500}>
              #{tagName || t('tag')}
            </Typography>
          )}

          {folderName && (
            <Typography fontSize={20} fontWeight={500}>
              {folderName || t('savedPosts')}
            </Typography>
          )}
        </Box>

        {tagName && (
          <Button
            variant="outlined"
            onClick={handleFollowClick}
            sx={{
              height: '32px',
              borderRadius: '8px',
              fontSize: '16px',
              fontWeight: 700,
              padding: '0 12px',
              textTransform: 'none',
              whiteSpace: 'nowrap',
              ...(isFollowing
                ? {
                    backgroundColor: 'white',
                    color: '#A24295',
                    borderColor: '#A24295',
                  }
                : {
                    backgroundColor: '#A24295',
                    color: 'white',
                    borderColor: '#A24295',
                  }),
            }}
          >
            {isFollowing ? t('following') : t('follow')}
          </Button>
        )}
      </Box>
    );
  }

  if (variant === 'saved-posts') {
    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          backgroundColor: 'white',
          px: 2,
          py: 1.5,
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <BackButton onClick={() => router.back()} />
          <Typography fontSize={20} fontWeight={500}>
            {t('savedPosts')}
          </Typography>
        </Box>
        <Image
          src={FOLDER_ICON}
          alt="Folder Icon"
          width={24}
          height={24}
          onClick={() => setActiveDialog('NewFolder')}
        />
      </Box>
    );
  }

  return null;
}
