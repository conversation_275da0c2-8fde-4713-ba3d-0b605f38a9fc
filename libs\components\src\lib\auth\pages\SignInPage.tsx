'use client';

import AuthLayout from '../components/AuthLayout';
import Welcome from '../components/Welcome';
import Subtitle from '../components/Subtitle';
import AuthTabs from '../components/AuthTabs';
import { Box } from '@mui/material';
import AuthTabContent from '../components/AuthTabContent';

import { useState } from 'react';

interface SignInPageTranslations {
  title: string;
  subtitle: string;
  emailLabel: string;
  passwordLabel: string;
  forgotPassword: string;
  continueLabel: string;
  orLabel: string;
  googleLabel: string;
  appleLabel: string;
  signIn: string;
  signUp: string;
}

interface SignInPageProps {
  onNavigate: (path: string) => void;
  onSignIn?: (data: { email: string; password: string }) => void;
  onForgotPassword?: () => void;
  isLoading?: boolean;
  error?: string | null;
  translations?: SignInPageTranslations;
  initialActiveTab?: number;
}

/**
 * SignInPage component that combines all elements for the sign in page
 */
export const SignInPage = (props: SignInPageProps) => {
  const {
    onSignIn = () => {
      // Intentional no-op: Default implementation
    },
    onForgotPassword = () => {
      // Intentional no-op: Default implementation
    },
    onNavigate = () => {
      // Intentional no-op: Default implementation
    },
    isLoading = false,
    error = null,
    translations,
  } = props;
  const [activeTab, setActiveTab] = useState(props.initialActiveTab || 0); // Track active tab (0: Sign In, 1: Sign Up Type, 2: Sign Up)

  const handleTabChange = (newTab: number) => {
    if (newTab !== activeTab) {
      setActiveTab(newTab);

      // Update the URL based on the selected tab after a small delay
      // to allow the animation to start smoothly

      if (newTab === 0) {
        onNavigate('/signin');
      } else if (newTab === 1) {
        onNavigate('/signup');
      }
    }
  };

  // Use provided translations or fallback to default values
  const t = translations || {
    title: 'Welcome to MiniCardiac',
    subtitle: 'The heart of cardiac healthcare',
    emailLabel: 'Email',
    passwordLabel: 'Password',
    forgotPassword: 'Forgot Password?',
    continueLabel: 'Continue',
    orLabel: 'OR',
    googleLabel: 'Continue with Google',
    appleLabel: 'Continue with Apple',
    signIn: 'Sign in',
    signUp: 'Sign up',
  };

  return (
    <AuthLayout activeTab={activeTab}>
      <Box
        sx={{
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
          '&::-webkit-scrollbar': {
            display: 'none',
          },
          mt: { xs: '40px', sm: '40px', xxl: '100px' },
        }}
      >
        {/* Welcome Text */}
        <Welcome title={t.title} />

        {/* Subtitle */}
        <Subtitle text={t.subtitle} />

        {/* Sign In/Up Tabs */}
        <AuthTabs
          value={activeTab}
          onChange={handleTabChange}
          signInLabel={t.signIn}
          signUpLabel={t.signUp}
        />
      </Box>

      <AuthTabContent
        activeTab={activeTab}
        onSubmit={onSignIn}
        onForgotPassword={onForgotPassword}
        onTypeSelect={onNavigate}
        isLoading={isLoading}
        error={error}
        emailLabel={t.emailLabel}
        passwordLabel={t.passwordLabel}
        forgotPasswordLabel={t.forgotPassword}
        continueLabel={t.continueLabel}
        orLabel={t.orLabel}
        googleLabel={t.googleLabel}
        appleLabel={t.appleLabel}
      />
    </AuthLayout>
  );
};

export default SignInPage;
