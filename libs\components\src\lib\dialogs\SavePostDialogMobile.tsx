'use client';

import {
  <PERSON>,
  But<PERSON>,
  Dialog,
  DialogContent,
  <PERSON>Field,
  Typography,
} from '@mui/material';
import Image from 'next/image';
import { useState } from 'react';
import Checkbox from '../checkbox/Checkbox';
import { FOLDER_ICON, MEDIA_1 } from '../auth';
import { useTranslations } from 'next-intl';

const folders = [
  'Important Articles',
  'Surgery',
  'New Procedures',
  'Cardiology',
  'Patient Notes',
  'Weekly Reviews',
  'Case Studies',
  'Quick Reads',
];

const SavePostDialogMobile = ({
  open,
  onClose,
}: {
  open: boolean;
  onClose: () => void;
}) => {
  const t = useTranslations('savePost');

  const [newFolderName, setNewFolderName] = useState('');
  const [selectedFolders, setSelectedFolders] = useState<string[]>([]);

  const handleToggleFolder = (folder: string) => {
    setSelectedFolders((prev) =>
      prev.includes(folder)
        ? prev.filter((item) => item !== folder)
        : [...prev, folder]
    );
  };

  const canSave = newFolderName.trim() !== '' || selectedFolders.length > 0;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      hideBackdrop
      PaperProps={{
        sx: {
          position: 'fixed',
          bottom: -30,
          left: 0,
          right: 0,
          mx: 'auto',
          width: '100%',
          borderTopLeftRadius: '40px',
          borderTopRightRadius: '40px',
          height: '664px',
          backgroundColor: '#fff',
          zIndex: 1300,
        },
      }}
    >
      <DialogContent
        sx={{
          padding: '16px',
          pt: '47px',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <Typography fontSize={24} fontWeight={600} mb={4}>
          {t('title')}
        </Typography>

        {/* Folder input */}
        <Box
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          mb={5}
        >
          <TextField
            label={t('folderLabel')}
            placeholder={t('folderPlaceholder')}
            value={newFolderName}
            onChange={(e) => setNewFolderName(e.target.value)}
            InputLabelProps={{ shrink: true }}
            sx={{ flex: 1, mr: 2, height: '45px' }}
          />
          <Image src={FOLDER_ICON} alt="Folder Icon" width={24} height={24} />
        </Box>

        {/* Folder list */}
        <Box
          display={'flex'}
          flexDirection={'column'}
          overflow="auto"
          sx={{
            '&::-webkit-scrollbar': { display: 'none' },
            scrollbarWidth: 'none',
            gap: '20px',
          }}
        >
          {folders.map((folder) => {
            const isChecked = selectedFolders.includes(folder);
            return (
              <Box
                key={folder}
                display="flex"
                alignItems="center"
                justifyContent="space-between"
                sx={{
                  backgroundColor: '#F8F9FA',
                  borderRadius: '8px',
                  padding: '16px',
                  height: '84px',
                }}
                onClick={() => handleToggleFolder(folder)}
              >
                <Box display="flex" alignItems="center">
                  <Image
                    src={MEDIA_1}
                    alt="Folder"
                    width={52}
                    height={52}
                    style={{ marginRight: '12px', borderRadius: '8px' }}
                  />
                  <Typography
                    fontSize="16px"
                    fontWeight={isChecked ? 700 : 400}
                    color="#1E1E1E"
                  >
                    {folder}
                  </Typography>
                </Box>
                <Checkbox
                  key={folder}
                  checked={isChecked}
                  onToggle={() => handleToggleFolder(folder)}
                  sx={{ width: '20px' }}
                />
              </Box>
            );
          })}
        </Box>

        {/* Buttons */}
        <Box
          mt={5}
          display="flex"
          flexDirection="column"
          gap="20px"
          alignItems="center"
        >
          <Button
            fullWidth
            variant="outlined"
            onClick={onClose}
            sx={{
              height: '40px',
              width: '160px',
              fontWeight: 700,
              borderColor: '#A24295',
              color: '#A24295',
              '&:hover': { backgroundColor: '#f9f0f5' },
            }}
          >
            {t('cancel')}
          </Button>
          <Button
            fullWidth
            variant="contained"
            onClick={onClose}
            sx={{
              height: '40px',
              width: '160px',
              fontWeight: 700,
              backgroundColor: canSave ? '#A24295' : '#A3A3A3',
              color: '#fff',
              '&:hover': { backgroundColor: '#932080' },
            }}
          >
            {t('save')}
          </Button>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default SavePostDialogMobile;
