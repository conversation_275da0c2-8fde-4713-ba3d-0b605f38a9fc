'use client';

import { Box } from '@mui/material';
import TagBox from './TagBox';
import { useTranslations } from 'next-intl';

const allYourTags = ['Surgery', 'AI', 'Heart Valve', 'Stent'];
const allSuggestedTags = [
  'Cardiology',
  'React',
  'Medical Imaging',
  'Cardio AI',
];

interface FullTagSectionViewProps {
  tagType: 'your-tags' | 'suggested';
}

export default function FullTagSectionView({
  tagType,
}: FullTagSectionViewProps) {
  const t = useTranslations('tagsPage');

  const tags = tagType === 'your-tags' ? allYourTags : allSuggestedTags;
  const isSuggested = tagType === 'suggested';

  return (
    <Box
      sx={{
        display: 'flex',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
        gap: '20px',
      }}
    >
      {tags.map((tag) => (
        <Box
          key={tag}
          sx={{
            flex: '1 1 45%',
            minWidth: '300px',
            maxWidth: '100%',
          }}
        >
          <TagBox
            tag={tag}
            sx={{ backgroundColor: 'white', width: '100%' }}
            {...(isSuggested && {
              followButtonProps: {
                isFollowing: false,
                onClick: () => console.log(t('followedTag', { tag })),
              },
            })}
          />
        </Box>
      ))}
    </Box>
  );
}
