'use client';

import { useState } from 'react';
import {
  Box,
  Typography,
  Stack,
  Menu,
  MenuItem,
  useMediaQuery,
} from '@mui/material';
import CustomDialog from '../CustomDialog';
import { LoadingButton } from '../../loading-button';
import { Iconify } from '../../iconify';
import { PostButton } from '../../buttons/PostButton';
import { toast } from 'react-toastify';
import { isContentEmpty } from '@minicardiac-client/utilities';
import { CustomizedSteppers } from '../../onboarding';
import ArticlePostDetails from './ArticlePostDetails';
import { CustomEditorBase } from '../../textEditor/CustomEditorBase';
import { BackButton } from '../../buttons/Backbutton';
import { useTheme } from '@emotion/react';
import { useTranslations } from 'next-intl';
import { useCreateArticlePost } from '@minicardiac-client/apis';
import { uploadImageToS3 } from '../../image/utils';
import SchedulePostDialog from '../ScheduleDialog';

interface ArticlePostDialogProps {
  open: boolean;
  onClose: () => void;
  setOpenScheduleDialog: () => void;
  content: string;
  setContent: (content: string) => void;
}

const ArticlePostDialog = ({
  open,
  onClose,
  setOpenScheduleDialog,
  content,
  setContent,
}: ArticlePostDialogProps) => {
  const t = useTranslations('articlePost');

  const [tags, setTags] = useState('');
  const [activeStep, setActiveStep] = useState(0);
  const [thumbnail, setThumbnail] = useState<File | null>(null);
  const [audience, setAudience] = useState('PROFESSIONAL');
  const [speciality, setSpeciality] = useState('CARDIAC_SURGERY');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const openMenu = Boolean(anchorEl);
  const [postScheduleDate, setPostScheduleDate] = useState<string | undefined>(undefined);
  const [scheduleDialogOpen, setScheduleDialogOpen] = useState(false);

  const [title, setTitle] = useState('');
  const [summary, setSummary] = useState('');

  const theme: any = useTheme();
  const screenBelowSM = useMediaQuery(theme.breakpoints.down('sm'));

  const canPost = Boolean(title.trim() && summary.trim());

  const handleNextStep = () => {
    setActiveStep((prev) => prev + 1);
  };

  const createArticlePost = useCreateArticlePost({
    onSuccess: () => {
      toast.success('Article post created successfully!');
      onClose();
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to create article post');
    },
  });

  const audienceMap: Record<string, string> = {
    CARDIAC_SURGEON: 'CARDIAC_SURGEON',
    CARDIOLOGIST: 'CARDIOLOGIST',
    BOTH: 'BOTH',
    CARDIOLOGY: 'CARDIOLOGIST',
    CARDIAC_SURGERY: 'CARDIAC_SURGEON',
  };

  const handleSchedule = async (isoDate: string) => {
    setPostScheduleDate(isoDate);
    setScheduleDialogOpen(false);
    await handlePostWithSchedule(isoDate);
  };

  const handlePostWithSchedule = async (isoDate: string) => {
    let coverImagePath: string | undefined = undefined;
    if (thumbnail) {
      try {
        coverImagePath = await uploadImageToS3(thumbnail);
      } catch {
        toast.error('Image upload failed');
        return;
      }
    }
    const bodyContent = content || summary || title || 'Article content';
    const payload = {
      community: (audience || 'PROFESSIONAL') as 'PROFESSIONAL' | 'PUBLIC' | 'BOTH',
      audience: (audienceMap[speciality] || 'BOTH') as 'CARDIAC_SURGEON' | 'CARDIOLOGIST' | 'BOTH',
      tags: Array.isArray(tags) ? tags : tags.split(/[,\s]+/).filter(Boolean),
      postStatus: 'scheduled' as const,
      postScheduleDate: isoDate,
      title: title.trim(),
      content: summary.trim(),
      body: bodyContent,
      coverImagePath,
    };
    createArticlePost.mutate(payload);
  };

  const handlePost = async () => {
    let coverImagePath: string | undefined = undefined;
    if (thumbnail) {
      try {
        coverImagePath = await uploadImageToS3(thumbnail);
      } catch {
        toast.error('Image upload failed');
        return;
      }
    }

    // Ensure body has enough content (at least 100 characters)
    const bodyContent = content || summary || title || 'Article content';

    const postStatus: 'draft' | 'published' | 'scheduled' = postScheduleDate ? 'scheduled' : 'published';
    const payload = {
      community: (audience || 'PROFESSIONAL') as 'PROFESSIONAL' | 'PUBLIC' | 'BOTH',
      audience: (audienceMap[speciality] || 'BOTH') as 'CARDIAC_SURGEON' | 'CARDIOLOGIST' | 'BOTH',
      tags: Array.isArray(tags) ? tags : tags.split(/[,\s]+/).filter(Boolean),
      postStatus,
      postScheduleDate,
      title: title.trim(),
      content: summary.trim(),
      body: bodyContent,
      coverImagePath,
    };
    createArticlePost.mutate(payload);
  };

  return (
    <CustomDialog
      open={open}
      onClose={onClose}
      title=""
      sx={{
        p: 0,
        px: { xs: 0, sm: '80px' },
        pt: { xs: 0, sm: '50px' },
        alignItems: { xs: 'stretch', sm: 'start' },
        '.MuiDialog-paper': {
          maxHeight: { xs: '100%', sm: 'calc(100% - 64px)' },
        },
      }}
    >
      <Box
        display="flex"
        flexDirection="column"
        padding={{ xs: '16px', sm: '40px' }}
        width="100%"
        sx={{ backgroundColor: 'white' }}
      >
        {/* Heading */}
        <Box
          position="relative"
          display="flex"
          justifyContent={{ xs: 'space-between', sm: 'center' }}
          alignItems="center"
          height="35px"
          mb={'20px'}
        >
          <Box sx={{ display: 'flex', gap: '16px', alignItems: 'center' }}>
            {screenBelowSM && <BackButton onClick={onClose} />}
            <Typography
              sx={{
                fontFamily: 'Plus Jakarta Sans',
                fontWeight: 500,
                fontSize: { xs: '20px', sm: '28px' },
                color: '#1E1E1E',
              }}
            >
              {t('newArticlePost')}
            </Typography>
          </Box>

          <Typography
            sx={{
              position: 'absolute',
              right: 0,
              top: { xs: 10, sm: 0 },
              fontFamily: 'Plus Jakarta Sans',
              fontWeight: 600,
              fontSize: '16px',
              color: '#A24295',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
            }}
          >
            {t('drafts')} <Iconify icon="solar:arrow-right-linear" />
          </Typography>
        </Box>

        <CustomizedSteppers
          activeStep={activeStep}
          steps={[t('stepCompose'), t('stepDetails')]}
        />

        {activeStep === 0 ? (
          <CustomEditorBase
            value={content}
            onChange={setContent}
            label={t('editorLabel')}
            placeholder={t('editorPlaceholder')}
            sx={{
              maxHeight: { xs: '589px', sm: '420px' },
              mt: '20px',
              mb: { xs: '140px', sm: '0px' },
            }}
            menuType={'article'}
          />
        ) : (
          <ArticlePostDetails
            tags={tags}
            setTags={setTags}
            audience={audience}
            setAudience={setAudience}
            speciality={speciality}
            setSpeciality={setSpeciality}
            thumbnail={thumbnail}
            setThumbnail={setThumbnail}
            title={title}
            setTitle={setTitle}
            summary={summary}
            setSummary={setSummary}
          />
        )}

        {/* Footer Buttons */}
        <Stack
          direction={{ xs: 'column', sm: 'row' }}
          spacing={{ xs: '10px', sm: '20px' }}
          justifyContent={{ xs: 'center', sm: 'center' }}
          sx={{
            position: { xs: 'fixed', sm: 'static' },
            bottom: 0,
            left: 0,
            mt: { xs: '0px', sm: '40px' },
            width: { xs: '100%', sm: 'auto' },
            background: { xs: 'rgba(255,255,255,0.8)', sm: 'transparent' },
            backdropFilter: { xs: 'blur(20px)', sm: 'none' },
            padding: { xs: '20px', sm: 0 },
            boxShadow: { xs: '0 -4px 20px 0 rgba(0,0,0,0.1)', sm: 'none' },
          }}
        >
          <Box
            sx={{
              display: 'flex',
              width: '100%',
              justifyContent: 'center',
              alignItems: 'center',
              flexDirection: { xs: 'column', sm: 'row' },
              gap: '20px',
            }}
          >
            <LoadingButton
              variant="outlined"
              onClick={onClose}
              sx={{
                width: '156px',
                height: '40px',
                backgroundColor: 'white',
                border: { xs: 'none', sm: '1px solid #A24295' },
                color: '#A24295',
                '&:hover': {
                  backgroundColor: 'secondary.light',
                },
                fontSize: '16px',
                fontWeight: 700,
              }}
            >
              {t('cancel')}
            </LoadingButton>

            {activeStep === 0 ? (
              <LoadingButton
                onClick={handleNextStep}
                variant="contained"
                disabled={isContentEmpty(content)}
                sx={{
                  width: '156px',
                  height: '40px',
                  backgroundColor: isContentEmpty(content) ? '#ccc' : '#A24295',
                  color: 'white',
                  fontSize: '16px',
                  fontWeight: 700,
                  '&:hover': {
                    backgroundColor: isContentEmpty(content)
                      ? '#ccc'
                      : '#8d2a7b',
                  },
                }}
              >
                {t('next')}
              </LoadingButton>
            ) : (
              <>
                <PostButton
                  setAnchorEl={setAnchorEl}
                  handlePost={handlePost}
                  disabled={!canPost}
                  isOpen={openMenu}
                />
                <Menu
                  anchorEl={anchorEl}
                  open={openMenu}
                  onClose={() => setAnchorEl(null)}
                >
                  <MenuItem onClick={() => setScheduleDialogOpen(true)}>
                    {t('schedule')}
                  </MenuItem>
                  <MenuItem onClick={() => setAnchorEl(null)}>
                    {t('saveDraft')}
                  </MenuItem>
                  <MenuItem onClick={() => setAnchorEl(null)}>
                    {t('addToSponsorship')}
                  </MenuItem>
                </Menu>
              </>
            )}
          </Box>
        </Stack>
      </Box>
      <SchedulePostDialog
        open={scheduleDialogOpen}
        onClose={() => setScheduleDialogOpen(false)}
        onSchedule={handleSchedule}
      />
    </CustomDialog>
  );
};

export default ArticlePostDialog;