'use client';

import { useState } from 'react';
import {
  Box,
  Button,
  Dialog,
  DialogContent,
  TextField,
  Typography,
} from '@mui/material';
import { useTranslations } from 'next-intl';

interface AddNewFolderDialogProps {
  open: boolean;
  onClose: () => void;
}

export default function AddNewFolderDialog({
  open,
  onClose,
}: AddNewFolderDialogProps) {
  const [folderName, setFolderName] = useState('');
  const t = useTranslations('newFolderDialog');
  const canSave = folderName.trim() !== '';

  return (
    <Dialog
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          width: '100%',
          maxWidth: '596px',
          maxHeight: '297px',
          borderRadius: '12px',
          backgroundColor: '#fff',
          padding: '20px',
        },
      }}
    >
      <DialogContent sx={{ padding: 0 }}>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-between',
            height: '100%',
          }}
        >
          {/* Header */}
          <Typography
            fontSize="24px"
            fontWeight={500}
            color="#1E1E1E"
            fontFamily="Plus Jakarta Sans"
          >
            {t('title')}
          </Typography>

          {/* Input Field */}
          <Box mt="24px">
            <TextField
              label={t('folderLabel')}
              placeholder={t('folderPlaceholder')}
              value={folderName}
              onChange={(e) => setFolderName(e.target.value)}
              InputLabelProps={{ shrink: true }}
              fullWidth
            />
          </Box>

          {/* Footer Buttons */}
          <Box mt="24px" display="flex" justifyContent="center" gap="16px">
            <Button
              variant="outlined"
              onClick={onClose}
              sx={{
                width: '156px',
                height: '40px',
                fontWeight: 700,
                borderColor: '#A24295',
                color: '#A24295',
                '&:hover': {
                  backgroundColor: '#f9f0f5',
                },
              }}
            >
              {t('cancel')}
            </Button>
            <Button
              variant="contained"
              onClick={onClose}
              disabled={!canSave}
              sx={{
                width: '156px',
                height: '40px',
                fontWeight: 700,
                backgroundColor: canSave ? '#A24295' : '#A3A3A3',
                color: '#fff',
                '&:hover': {
                  backgroundColor: canSave ? '#932080' : '#A3A3A3',
                },
              }}
            >
              {t('save')}
            </Button>
          </Box>
        </Box>
      </DialogContent>
    </Dialog>
  );
}
