'use client';

import { usePathname, useRouter } from 'next/navigation';
import { useLocale } from 'next-intl';
import { useState } from 'react';
import {
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Box,
  IconButton,
  Grid,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import Image from 'next/image';
import { getCdnUrl } from '@minicardiac-client/utilities';

const locales = [
  {
    code: 'en',
    label: 'UK (Eng)',
    name: 'English',
    src: getCdnUrl('assets/flags/UK-flag.jpg'),
  },
  {
    code: 'fr',
    label: 'France (Fr)',
    name: 'Français',
    src: getCdnUrl('assets/flags/FR-flag.jpg'),
  },
];

export default function LocaleSwitcher() {
  const currentLocale = useLocale();
  const router = useRouter();
  const pathname = usePathname();
  const [open, setOpen] = useState(false);
  const [selectedLocale, setSelectedLocale] = useState(currentLocale);

  const current = locales.find((l) => l.code === currentLocale) || locales[0];

  const handleProceed = () => {
    const newPath = `/${selectedLocale}${pathname?.slice(3)}`;
    router.replace(newPath);
    setOpen(false);
  };

  return (
    <>
      <Button
        variant="outlined"
        size="small"
        onClick={() => setOpen(true)}
        sx={{
          color: '#333537',
          mt: 6,
          mx: 'auto',
          textTransform: 'none',
          borderRadius: '2px',
          border: '1px solid #333537',
          padding: '4px 8px',
          minWidth: 0,
          maxWidth: '90px',
          height: '32px',
        }}
        startIcon={
          <Image
            src={current.src}
            alt={current.label}
            width={20}
            height={15}
            style={{ objectFit: 'cover' }}
          />
        }
      >
        {current.name === 'English' ? 'ENG' : 'FR'}
      </Button>

      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle sx={{ m: 0, p: 2 }}>
          Languages
          <IconButton
            aria-label="close"
            onClick={() => setOpen(false)}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent>
          <Grid container spacing={2} justifyContent="center">
            {locales.map((locale) => {
              const isSelected = selectedLocale === locale.code;

              return (
                <Grid key={locale.code}>
                  <Button
                    variant="outlined"
                    onClick={() => setSelectedLocale(locale.code)}
                    sx={{
                      width: 'fit-content',
                      height: '40px',
                      borderRadius: '8px',
                      textTransform: 'none',
                      justifyContent: 'flex-start',
                      paddingLeft: 1.5,
                      color: 'inherit',
                      borderColor: isSelected ? '#A24295' : '#A3A3A3',
                      borderWidth: isSelected ? '3px' : '2px',
                    }}
                  >
                    <Box
                      sx={{ display: 'flex', alignItems: 'center', gap: 1.2 }}
                    >
                      <Image
                        src={locale.src}
                        alt={locale.label}
                        width={24}
                        height={16}
                        style={{ objectFit: 'cover' }}
                      />
                      <Typography
                        variant="body2"
                        sx={{ fontSize: '0.75rem', color: 'inherit' }}
                      >
                        {locale.label}
                      </Typography>
                      <Typography
                        variant="caption"
                        color={isSelected ? 'inherit' : 'text.secondary'}
                        sx={{ fontSize: '0.75rem' }}
                      >
                        ({locale.name})
                      </Typography>
                    </Box>
                  </Button>
                </Grid>
              );
            })}
          </Grid>
        </DialogContent>

        <DialogActions sx={{ justifyContent: 'center', py: 2 }}>
          <Button
            variant="contained"
            onClick={handleProceed}
            sx={{ px: 5, borderRadius: 1 }}
          >
            Proceed
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
