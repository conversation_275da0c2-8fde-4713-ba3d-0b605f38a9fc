import React from 'react';
import parse, {
  domToReact,
  HTMLReactParserOptions,
  Element,
  Text,
} from 'html-react-parser';

interface HighlightHtmlProps {
  html: string;
  keyword: string;
  className?: string;
}

function highlightText(text: string, keyword: string, className?: string) {
  if (!keyword) return text;
  const regex = new RegExp(
    `(${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`,
    'gi'
  );
  const parts = text.split(regex);
  return parts.map((part, i) =>
    regex.test(part) ? (
      <mark
        key={i}
        className={className}
        style={{ fontWeight: 'bold', background: 'none', color: 'inherit' }}
      >
        {part}
      </mark>
    ) : (
      part
    )
  );
}

export const HighlightHtml: React.FC<HighlightHtmlProps> = ({
  html,
  keyword,
  className,
}) => {
  if (!keyword) return <>{parse(html)}</>;

  const options: HTMLReactParserOptions = {
    replace: (domNode) => {
      if (domNode.type === 'text') {
        const textNode = domNode as Text;
        return <>{highlightText(textNode.data, keyword, className)}</>;
      }
      // For elements, recursively parse children
      if (domNode.type === 'tag') {
        const el = domNode as Element;
        if (el.children && el.children.length > 0) {
          return React.createElement(
            el.name,
            el.attribs,
            domToReact(el.children as any, options)
          );
        }
      }
      return undefined;
    },
  };

  return <>{parse(html, options)}</>;
};
