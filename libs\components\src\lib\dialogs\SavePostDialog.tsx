'use client';

import { useTheme, useMediaQuery } from '@mui/material';
import SavePostDialogMobile from './SavePostDialogMobile';
import SavePostDialogDesktop from './SavePostDialogDesktop';

const SavePostDialog = ({
  open,
  onClose,
}: {
  open: boolean;
  onClose: () => void;
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return isMobile ? (
    <SavePostDialogMobile open={open} onClose={onClose} />
  ) : (
    <SavePostDialogDesktop open={open} onClose={onClose} />
  );
};

export default SavePostDialog;
