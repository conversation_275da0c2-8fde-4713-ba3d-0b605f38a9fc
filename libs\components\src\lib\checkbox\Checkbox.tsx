import {
  Box,
  Checkbox as M<PERSON>Check<PERSON>,
  FormControlLabel,
  Typography,
  SxProps,
} from '@mui/material';

interface Props {
  label?: string;
  checked: boolean;
  onToggle: () => void;
  sx?: SxProps;
}

const Checkbox = ({ label, checked, onToggle, sx }: Props) => {
  return (
    <FormControlLabel
      sx={{
        width: 'calc(50% - 8px)',
        height: '40px',
        alignItems: 'center',
        margin: 0,
        padding: 0,
        ...sx,
      }}
      control={
        <Box
          sx={{
            width: '40px',
            height: '40px',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <MUICheckbox
            checked={checked}
            onChange={onToggle}
            sx={{
              width: '20px',
              height: '20px',
              borderRadius: '8px',
              color: '#A3A3A3',
              '&:hover': {
                backgroundColor: 'transparent',
              },
              '&.Mui-checked': {
                color: 'white',
                backgroundColor: 'white',
                '& svg': {
                  fill: '#A24295',
                },
              },
            }}
          />
        </Box>
      }
      label={
        <Typography
          sx={{
            fontSize: '16px',
            fontWeight: 400,
            color: '#1E1E1E',
            fontFamily: 'Plus Jakarta Sans',
          }}
        >
          {label}
        </Typography>
      }
    />
  );
};

export default Checkbox;
