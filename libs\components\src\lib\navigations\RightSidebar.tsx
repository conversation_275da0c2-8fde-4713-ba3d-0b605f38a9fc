// components/sidebar/RightSidebar.tsx
'use client';
import React from 'react';
import { Box } from '@mui/material';
import RightSidebarTags from '../tags/RightSidebarTags';
import RightSidebarFolders from './RightSidebarFolders';

interface RightSidebarProps {
  variant?: 'tags' | 'folders';
}

const RightSidebar: React.FC<RightSidebarProps> = ({ variant = 'tags' }) => {
  return (
    <Box
      sx={{
        display: { xs: 'none', lg: 'flex' },
        flexDirection: 'column',
        position: 'relative',
      }}
    >
      {variant === 'tags' ? <RightSidebarTags /> : <RightSidebarFolders />}
    </Box>
  );
};

export default RightSidebar;
