'use client';

import { useTheme } from '@emotion/react';
import { Box, TextField, useMediaQuery } from '@mui/material';
import { useTranslations } from 'next-intl';

interface MediaPostCaptionProps {
  images: File[];
  caption: string;
  setCaption: (value: string) => void;
}

const MediaPostCaption = ({
  images,
  caption,
  setCaption,
}: MediaPostCaptionProps) => {
  const theme: any = useTheme();
  const screenBelowSM = useMediaQuery(theme.breakpoints.down('sm'));
  const t = useTranslations('mediaPost');

  return (
    <Box display="flex" flexDirection="column" alignItems="center" width="100%">
      {/* Images Section */}
      <Box
        width="100%"
        sx={{
          overflowX: 'auto',
          scrollbarWidth: 'none',
          textAlign: 'center',
          '&::-webkit-scrollbar': { display: 'none' },
        }}
      >
        <Box
          display="inline-flex"
          gap="20px"
          p={screenBelowSM ? 0 : '8px'}
          height={screenBelowSM ? '277px' : '132px'}
          borderRadius="8px"
          ml={screenBelowSM ? '36px' : 'auto'}
          mr={screenBelowSM ? 0 : 'auto'}
          sx={{
            width: 'fit-content',
            border: screenBelowSM ? 'none' : '1px solid #E0E0E0',
          }}
        >
          {images.map((file, index) => {
            const src = URL.createObjectURL(file);
            return (
              <Box
                key={index}
                component="img"
                src={src}
                alt={`upload-preview-${index}`}
                width={screenBelowSM ? '289px' : '116px'}
                height={screenBelowSM ? '277px' : '116px'}
                sx={{
                  objectFit: 'cover',
                  borderRadius: '8px',
                  flexShrink: 0,
                }}
              />
            );
          })}
        </Box>
      </Box>

      {/* Caption Input */}
      <Box mt="40px" mb={{ xs: '40px', sm: '0px' }} width="100%">
        <TextField
          label={t('caption')}
          placeholder={t('captionPlaceholder')}
          variant="outlined"
          size="small"
          multiline
          minRows={6}
          value={caption}
          onChange={(e) => setCaption(e.target.value)}
          InputLabelProps={{ shrink: true }}
          fullWidth
          sx={{
            '& .MuiOutlinedInput-root': {
              height: { xs: 181, sm: 160 },
              '& textarea': {
                height: '100%',
                boxSizing: 'border-box',
                resize: 'none',
                paddingTop: '10px',
                '&::placeholder': {
                  textAlign: 'left',
                  verticalAlign: 'top',
                  paddingTop: 0,
                  lineHeight: 1.2,
                },
              },
            },
          }}
        />
      </Box>
    </Box>
  );
};

export default MediaPostCaption;
