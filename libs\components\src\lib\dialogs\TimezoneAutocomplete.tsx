import { Autocomplete, TextField, Box, CircularProgress } from '@mui/material';
import { useEffect, useState } from 'react';
import { Iconify } from '../iconify';

const timezones = [
  'GMT +5:30 India (Default)',
  'GMT +0:00 UTC',
  'GMT +0:00 UK (England)',
  'GMT -8:00 PST',
];

interface TimezoneAutocompleteProps {
  selectedTimezone?: string;
  onTimezoneChange?: (timezone: string) => void;
}

const TimezoneAutocomplete = ({
  selectedTimezone = timezones[0],
  onTimezoneChange,
}: TimezoneAutocompleteProps) => {
  const [timezone, setTimezone] = useState(selectedTimezone);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [open, setOpen] = useState(false);

  useEffect(() => {
    if (inputValue) {
      setIsLoading(true);
      const timer = setTimeout(() => {
        setIsLoading(false);
      }, 500);
      return () => clearTimeout(timer);
    }

    setIsLoading(false);
    return undefined;
  }, [inputValue]);

  return (
    <Box
      position="relative"
      sx={{
        width: { xs: '300px', lg: '345px' },
      }}
    >
      <Autocomplete
        size="small"
        open={open}
        onOpen={() => setOpen(true)}
        onClose={() => setOpen(false)}
        options={timezones}
        value={timezone}
        onChange={(_, newVal) => {
          if (newVal !== null) {
            setTimezone(newVal);
            onTimezoneChange?.(newVal);
          }
        }}
        inputValue={inputValue}
        onInputChange={(_, newInputVal) => setInputValue(newInputVal)}
        loading={isLoading}
        popupIcon={null}
        noOptionsText="No timezones found"
        renderInput={(params) => (
          <Box position="relative">
            <TextField
              {...params}
              label="Timezone"
              placeholder="Select timezone"
              variant="outlined"
              fullWidth
            />
            {/* Loader & icon container */}
            <Box
              position="absolute"
              right={12}
              top="50%"
              sx={{
                transform: 'translateY(-50%)',
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                pointerEvents: 'none',
              }}
            >
              {isLoading && <CircularProgress color="inherit" size={20} />}
              <Iconify
                icon="mdi:chevron-down"
                sx={{
                  color: '#A24295',
                  fontSize: 24,
                  transition: 'transform 0.3s ease',
                  display: open ? 'none' : 'block',
                }}
              />
            </Box>
          </Box>
        )}
      />
    </Box>
  );
};

export default TimezoneAutocomplete;
