import Box from '@mui/material/Box';
import AddPostOptions from './AddPostOptions';
import { useAuth } from '@minicardiac-client/apis';

type Props = {
  children?: React.ReactNode;
};

export default function MainContent({ children }: Props) {
  const { authState } = useAuth();

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: '20px',
        backgroundColor: '#F3F4F6',
        pt: { xs: '20px', sm: '0px' },
      }}
    >
      {/* Add posts */}
      {authState.isAuthenticated && (
        <Box
          sx={{
            borderRadius: '8px',
            boxShadow: '0px 4px 20px rgba(30, 30, 30, 0.1)',
            height: '102px',
            overflowX: 'scroll',
            backgroundColor: 'white',
            display: 'none',
            justifyContent: 'center',
            width: 'auto',
            scrollbarWidth: 'none',
            '&::-webkit-scrollbar': {
              display: 'none',
            },
            '@media (min-width:1100px)': {
              display: 'flex',
            },
          }}
        >
          <AddPostOptions />
        </Box>
      )}

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: '20px',
          flexGrow: 1,
          overflowY: 'auto',
          px: { xs: '20px', sm: '0px' },
        }}
      >
        {children}
      </Box>
    </Box>
  );
}
