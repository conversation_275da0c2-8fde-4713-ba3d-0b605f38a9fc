import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { axiosInstance } from '../http-client.js';
import type { CreateMediaPostRequest, MediaItem } from './types.js';

export function useCreateMediaPost(options?: { onSuccess?: (data: unknown) => void; onError?: (error: AxiosError | Error) => void }) {
  return useMutation({
    mutationFn: async ({ files, ...otherFields }: { files: File[] } & Omit<CreateMediaPostRequest, 'medias'>) => {
      const medias: MediaItem[] = [];

      for (const [index, file] of files.entries()) {
        // Get presigned POST policy
        const { data: presignResponse } = await axiosInstance.post('/utils/generate-upload-url', {
          mediaType: file.type, 
          entityType: 'post',   
        });
        const { url, fields } = presignResponse.data;

        //  Upload to S3 using multipart/form-data POST
        const formData = new FormData();
        Object.entries(fields).forEach(([k, v]) => formData.append(k, v as string));
        formData.append('file', file);

        const uploadRes = await fetch(url, {
          method: 'POST',
          body: formData,
        });
        if (!uploadRes.ok) throw new Error('Failed to upload file to S3');

        medias.push({
          mediaPath: fields.key,
          mediaType: file.type,
          order: index,
          // altText: ...
        });
      }

      // Ensure tags is an array of unique strings
      let tagsArray: string[] = [];
      if (Array.isArray(otherFields.tags)) {
        tagsArray = Array.from(new Set(otherFields.tags));
      } else if (typeof otherFields.tags === 'string') {
        tagsArray = Array.from(new Set((otherFields.tags as string).split(/[ ,]+/).filter(Boolean)));
      }

      //  Create media post
      const payload: CreateMediaPostRequest = {
        ...otherFields,
        medias,
        tags: tagsArray,
      };
      const { data: postData } = await axiosInstance.post('/posts/media', payload);
      return postData;
    },
    onSuccess: options?.onSuccess,
    onError: options?.onError,
  });
} 