import { useQuery } from '@tanstack/react-query';
import { postApi } from './post-api.js';
import { FeedPostType } from '@minicardiac-client/types';

export function useSinglePost(postId: string, enabled = true) {
  return useQuery<FeedPostType>({
    queryKey: ['singlePost', postId],
    queryFn: async () => {
      const post = await postApi.getPostById(postId);
      return {
        ...post,
        isLiked: post.isLiked || false,
        likesCount: post.likesCount || 0,
        commentsCount: post.commentsCount || 0,
        shareCount: post.shareCount || 0,
        repostCount: post.repostCount || 0,
        publisherName: post.publisherName || post.username || 'Anonymous',
        title: post.title || '',
      };
    },
    enabled: !!postId && enabled,
  });
}
