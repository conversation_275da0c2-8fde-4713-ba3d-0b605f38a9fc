'use client';

import React from 'react';
import { Box } from '@mui/material';
import { useRouter } from '@/apps/public/src/i18n/navigation';
import { uploadDocuments, refreshSession } from '@minicardiac-client/apis';
import { useSnackbar, DocumentUploadPageTemplate, ActionButtonsTemplate } from '@minicardiac-client/components';
import DocumentUploadForm from '@/libs/components/src/lib/onboarding/components/Documents/DocumentUpload';


export default function StudentDocumentUploadPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const { showSuccess, showError } = useSnackbar();

  const handleDoThisLater = () => {
    // Navigate to feed page
    router.push('/feed');
  };

  const handleContinue = async () => {
    // Set submitting state
    setIsSubmitting(true);
    setError(null);

    try {

      // Properly refresh the session before making API calls




      try {
        await refreshSession(); // Ensure session cookie is fresh before upload
      } catch (err) {
        showError('Session expired. Please sign in again.');
        setIsSubmitting(false);
        return;
      }
      await uploadDocuments();

      // Show success message
      showSuccess('Documents saved successfully!');

      // Navigate to the next step in the onboarding flow after a short delay
      setTimeout(() => {
        router.push('/student/network');
      }, 1000);
    } catch (err: any) {
      console.error('Error saving document data:', err);
      let userFriendlyError = 'An unexpected error occurred. Please try again later.';
      if (err.response?.data?.message?.includes('values() must be called with at least one value')) {
        userFriendlyError = 'Something went wrong. Please try again.';
      } else if (err.response?.status === 403) {
        userFriendlyError = 'Access denied. Your session may have expired. Please try again.';
      }
      setError(userFriendlyError);
      showError(userFriendlyError);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <DocumentUploadPageTemplate
      userName="Stephie Crawford"
      subtitleText="Let's set up your Student Account!"
      showBackButton={false}
      currentStep={1}
      steps={['Profile Setup', 'Document Upload', 'Adding Network']}
    >
      {/* Document Upload Form */}
      <DocumentUploadForm hideSteppers={true} />

      {/* Action Buttons using shared template */}
      <ActionButtonsTemplate
        onSave={handleContinue}
        onSkip={handleDoThisLater}
        isSubmitting={isSubmitting}
        isValid={true}
        saveButtonText="Save and Continue"
        skipButtonText="Do this later"
        variant="student"
      />

      {/* Error Display */}
      {error && (
        <Box sx={{ color: 'error.main', mt: 2, textAlign: 'center' }}>
          {error}
        </Box>
      )}
    </DocumentUploadPageTemplate>
  );
}







