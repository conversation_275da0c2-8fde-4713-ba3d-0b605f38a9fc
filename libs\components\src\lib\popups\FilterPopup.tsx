import { useState } from 'react';
import { Box, Typography, ClickAwayListener } from '@mui/material';
import { LoadingButton } from '../loading-button';
import Checkbox from '../checkbox/Checkbox';
import { useFeedSearchStore } from '../store/useFeedSearchStore';

const filterOptions = [
  { label: 'Text posts', value: 'text' },
  { label: 'Poll posts', value: 'poll' },
  { label: 'Media posts', value: 'media' },
  { label: 'Article posts', value: 'article' },
  { label: 'Question posts', value: 'question' },
];

export default function FilterPopup({ onClose }: { onClose: () => void }) {
  const postTypes = useFeedSearchStore((state) => state.postTypes);
  const setPostTypes = useFeedSearchStore((state) => state.setPostTypes);
  const [selectedPostTypes, setSelectedPostTypes] =
    useState<string[]>(postTypes);

  const handleToggle = (option: string) => {
    setSelectedPostTypes((prev) =>
      prev.includes(option)
        ? prev.filter((item) => item !== option)
        : [...prev, option]
    );
  };

  const handleApply = () => {
    setPostTypes(selectedPostTypes);
    onClose();
  };

  return (
    <ClickAwayListener onClickAway={onClose}>
      <Box
        onClick={(e) => e.stopPropagation()}
        sx={{
          position: 'absolute',
          top: '40px',
          right: 0,
          width: '374px',
          height: '337px',
          border: '1px solid #A24295',
          borderRadius: '8px',
          backgroundColor: 'white',
          boxShadow: '0px 2px 12px rgba(0,0,0,0.1)',
          zIndex: 9999,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
        }}
      >
        <Box sx={{ p: '20px' }}>
          <Typography
            sx={{
              fontWeight: 500,
              fontSize: '20px',
              marginBottom: '16px',
              color: '#1E1E1E',
            }}
          >
            Filter by:
          </Typography>

          <Box flex={1} overflow="auto">
            <Box display="flex" flexWrap="wrap" gap="16px">
              {filterOptions.map((option) => (
                <Checkbox
                  key={option.value}
                  label={option.label}
                  checked={selectedPostTypes.includes(option.value)}
                  onToggle={() => handleToggle(option.value)}
                />
              ))}
            </Box>
          </Box>
        </Box>

        {/* Footer Buttons */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            gap: '20px',
            boxShadow: '0 -4px 20px rgba(0,0,0,0.1)',
            p: '20px',
          }}
        >
          <LoadingButton
            variant="outlined"
            onClick={onClose}
            sx={{
              width: { xs: '156px' },
              height: '40px',
              backgroundColor: 'white',
              border: '1px solid #A24295',
              color: '#A24295',
              '&:hover': {
                backgroundColor: 'secondary.light',
              },
              fontWeight: 700,
              fontSize: '16px',
            }}
          >
            Cancel
          </LoadingButton>
          <LoadingButton
            variant="contained"
            disabled={selectedPostTypes.length === 0}
            sx={{
              width: { xs: '156px' },
              height: '40px',
              borderRadius: '4px',
              backgroundColor: 'secondary.main',
              color: 'white',
              '&:hover': {
                backgroundColor: 'secondary.dark',
              },
              '&.Mui-disabled': {
                backgroundColor: 'neutral.300',
                color: 'neutral.700',
              },
              fontWeight: 700,
              fontSize: '16px',
            }}
            onClick={handleApply}
          >
            Apply
          </LoadingButton>
        </Box>
      </Box>
    </ClickAwayListener>
  );
}
