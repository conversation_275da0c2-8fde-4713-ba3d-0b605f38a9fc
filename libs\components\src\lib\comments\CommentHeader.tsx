'use client';

import { Box, Typography } from '@mui/material';

type CommentHeaderProps = {
  name: string;
  postedAgo: string;
  nameFontSize?: string;
  timeFontSize?: string;
};

const CommentHeader = ({
  name,
  postedAgo,
  nameFontSize = '16px',
  timeFontSize = '14px',
}: CommentHeaderProps) => {
  return (
    <Box display="flex" justifyContent="space-between" alignItems="center">
      <Typography fontSize={nameFontSize} fontWeight={600}>
        {name}
      </Typography>
      <Typography
        fontSize={timeFontSize}
        fontWeight={300}
        color="text.secondary"
      >
        {postedAgo}
      </Typography>
    </Box>
  );
};

export default CommentHeader;
